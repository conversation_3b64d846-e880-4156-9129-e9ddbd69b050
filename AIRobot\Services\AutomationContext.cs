using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using AIRobot.Core.Models;

namespace AIRobot.Core.Services;

/// <summary>
/// 自动化上下文实现
/// </summary>
public class AutomationContext : IAutomationContext
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AutomationContext> _logger;
    private readonly Dictionary<string, object> _variables = new();

    public AutomationContext(IServiceProvider serviceProvider, ILogger<AutomationContext> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public T GetService<T>() where T : class
    {
        return _serviceProvider.GetRequiredService<T>();
    }

    public void SetVariable(string name, object value)
    {
        _variables[name] = value;
        _logger.LogDebug("设置变量: {Name} = {Value}", name, value);
    }

    public T? GetVariable<T>(string name)
    {
        if (_variables.TryGetValue(name, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    public bool HasVariable(string name)
    {
        return _variables.ContainsKey(name);
    }

    public void Log(LogLevel level, string message)
    {
        _logger.Log(level, message);
    }

    public async Task DelayAsync(TimeSpan delay)
    {
        _logger.LogDebug("延迟 {Delay} 毫秒", delay.TotalMilliseconds);
        await Task.Delay(delay);
    }
}
