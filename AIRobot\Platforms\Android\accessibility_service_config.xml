<?xml version="1.0" encoding="utf-8"?>
<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:description="@string/accessibility_service_description"
    android:packageNames="com.android.systemui"
    android:accessibilityEventTypes="typeAllMask"
    android:accessibilityFlags="flagDefault|flagRetrieveInteractiveWindows|flagReportViewIds|flagRequestTouchExplorationMode"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:notificationTimeout="100"
    android:canRetrieveWindowContent="true"
    android:canRequestTouchExplorationMode="true"
    android:canRequestEnhancedWebAccessibility="true"
    android:canRequestFilterKeyEvents="true"
    android:canControlMagnification="true"
    android:canPerformGestures="true"
    android:canTakeScreenshot="true" />

<!-- 
服务描述：
- typeAllMask: 监听所有类型的辅助功能事件
- flagRetrieveInteractiveWindows: 获取交互式窗口信息
- flagReportViewIds: 报告视图ID
- flagRequestTouchExplorationMode: 请求触摸探索模式
- canRetrieveWindowContent: 可以检索窗口内容
- canRequestTouchExplorationMode: 可以请求触摸探索模式
- canRequestEnhancedWebAccessibility: 可以请求增强的Web辅助功能
- canRequestFilterKeyEvents: 可以请求过滤按键事件
- canControlMagnification: 可以控制放大功能
- canPerformGestures: 可以执行手势操作
- canTakeScreenshot: 可以截取屏幕截图
-->
