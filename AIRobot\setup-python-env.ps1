# AIRobot Python 环境设置脚本
# 用于配置 PaddleOCR 和相关依赖

param(
    [string]$PythonPath = "python",
    [switch]$Force
)

Write-Host "=== AIRobot Python 环境设置 ===" -ForegroundColor Green

# 检查 Python 是否安装
Write-Host "检查 Python 安装..." -ForegroundColor Yellow
try {
    $pythonVersion = & $PythonPath --version 2>&1
    Write-Host "找到 Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Error "未找到 Python。请确保 Python 3.8+ 已安装并在 PATH 中。"
    Write-Host "下载地址: https://www.python.org/downloads/" -ForegroundColor Cyan
    exit 1
}

# 检查 Python 版本
$versionMatch = $pythonVersion -match "Python (\d+)\.(\d+)"
if ($versionMatch) {
    $majorVersion = [int]$matches[1]
    $minorVersion = [int]$matches[2]
    
    if ($majorVersion -lt 3 -or ($majorVersion -eq 3 -and $minorVersion -lt 8)) {
        Write-Error "Python 版本过低。需要 Python 3.8 或更高版本。"
        exit 1
    }
} else {
    Write-Warning "无法解析 Python 版本，继续执行..."
}

# 创建虚拟环境
$venvPath = "python_env"
if (Test-Path $venvPath) {
    if ($Force) {
        Write-Host "删除现有虚拟环境..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force $venvPath
    } else {
        Write-Host "虚拟环境已存在。使用 -Force 参数重新创建。" -ForegroundColor Yellow
        $continue = Read-Host "是否继续使用现有环境? (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            exit 0
        }
    }
}

if (-not (Test-Path $venvPath)) {
    Write-Host "创建 Python 虚拟环境..." -ForegroundColor Yellow
    & $PythonPath -m venv $venvPath
    if ($LASTEXITCODE -ne 0) {
        Write-Error "创建虚拟环境失败"
        exit 1
    }
}

# 激活虚拟环境
$activateScript = Join-Path $venvPath "Scripts\Activate.ps1"
if (Test-Path $activateScript) {
    Write-Host "激活虚拟环境..." -ForegroundColor Yellow
    & $activateScript
} else {
    Write-Error "找不到激活脚本: $activateScript"
    exit 1
}

# 升级 pip
Write-Host "升级 pip..." -ForegroundColor Yellow
& python -m pip install --upgrade pip

# 安装依赖包
Write-Host "安装 PaddleOCR 依赖..." -ForegroundColor Yellow
& pip install -r python_requirements.txt

if ($LASTEXITCODE -ne 0) {
    Write-Error "安装依赖包失败"
    exit 1
}

# 验证安装
Write-Host "验证 PaddleOCR 安装..." -ForegroundColor Yellow
$testScript = @"
import paddleocr
import cv2
import numpy as np

print("PaddleOCR 版本:", paddleocr.__version__)
print("OpenCV 版本:", cv2.__version__)
print("NumPy 版本:", np.__version__)

# 测试 PaddleOCR 初始化
try:
    ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch')
    print("PaddleOCR 初始化成功")
except Exception as e:
    print("PaddleOCR 初始化失败:", str(e))
"@

$testScript | Out-File -FilePath "test_paddleocr.py" -Encoding UTF8
& python test_paddleocr.py

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ PaddleOCR 环境配置成功!" -ForegroundColor Green
} else {
    Write-Error "❌ PaddleOCR 环境配置失败"
    exit 1
}

# 清理测试文件
Remove-Item "test_paddleocr.py" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "=== 环境配置完成 ===" -ForegroundColor Green
Write-Host "虚拟环境路径: $((Get-Location).Path)\$venvPath" -ForegroundColor Cyan
Write-Host "激活命令: .\$venvPath\Scripts\Activate.ps1" -ForegroundColor Cyan
Write-Host ""
Write-Host "下一步: 运行 AIRobot 应用程序测试 OCR 功能" -ForegroundColor Yellow
