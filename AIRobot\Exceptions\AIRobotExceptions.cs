namespace AIRobot.Core.Exceptions;

/// <summary>
/// 自动化异常基类
/// </summary>
public class AutomationException : Exception
{
    public AutomationException(string message) : base(message) { }
    public AutomationException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 屏幕捕获异常
/// </summary>
public class ScreenCaptureException : AutomationException
{
    public ScreenCaptureException(string message) : base(message) { }
    public ScreenCaptureException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 输入模拟异常
/// </summary>
public class InputSimulationException : AutomationException
{
    public InputSimulationException(string message) : base(message) { }
    public InputSimulationException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// OCR 识别异常
/// </summary>
public class OcrException : AutomationException
{
    public OcrException(string message) : base(message) { }
    public OcrException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 计算机视觉异常
/// </summary>
public class ComputerVisionException : AutomationException
{
    public ComputerVisionException(string message) : base(message) { }
    public ComputerVisionException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 应用程序控制异常
/// </summary>
public class ApplicationControlException : AutomationException
{
    public ApplicationControlException(string message) : base(message) { }
    public ApplicationControlException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 任务执行异常
/// </summary>
public class TaskExecutionException : AutomationException
{
    public string TaskId { get; }
    public string StepId { get; }

    public TaskExecutionException(string taskId, string stepId, string message) : base(message)
    {
        TaskId = taskId;
        StepId = stepId;
    }

    public TaskExecutionException(string taskId, string stepId, string message, Exception innerException) 
        : base(message, innerException)
    {
        TaskId = taskId;
        StepId = stepId;
    }
}
