using AIRobot.Core.Models;

namespace AIRobot.Core.Interfaces;

/// <summary>
/// OCR 识别服务接口
/// </summary>
public interface IOcrService
{
    /// <summary>
    /// 识别图像中的文字
    /// </summary>
    Task<OcrResult> RecognizeTextAsync(byte[] imageData, OcrEngine engine = OcrEngine.Auto);
    
    /// <summary>
    /// 查找指定文字的位置
    /// </summary>
    Task<IEnumerable<TextRegion>> FindTextAsync(byte[] imageData, string searchText);
    
    /// <summary>
    /// 检查图像是否包含指定文字
    /// </summary>
    Task<bool> ContainsTextAsync(byte[] imageData, string searchText);
    
    /// <summary>
    /// 识别数学公式
    /// </summary>
    Task<FormulaResult> RecognizeFormulaAsync(byte[] imageData);
    
    /// <summary>
    /// 识别表格
    /// </summary>
    Task<TableResult> RecognizeTableAsync(byte[] imageData);
    
    /// <summary>
    /// 设置 OCR 语言
    /// </summary>
    Task SetLanguageAsync(string languageCode);
    
    /// <summary>
    /// 获取可用的 OCR 引擎
    /// </summary>
    Task<IEnumerable<OcrEngine>> GetAvailableEnginesAsync();
}
