using System;
using System.Drawing;
using System.Threading.Tasks;
using AIRobot.Core.Models;
using AIRobot.Core.Models.Steps;
using AIRobot.Platforms.Windows;
using Microsoft.Extensions.Logging;

namespace AIRobot.SimpleTest;

/// <summary>
/// 简单测试程序，验证核心功能
/// </summary>
public class SimpleTest
{
    public static async Task RunTestsAsync()
    {
        Console.WriteLine("🤖 AIRobot 简单测试开始");
        Console.WriteLine("========================");

        try
        {
            // 测试 1: 创建 Windows 屏幕捕获服务
            Console.WriteLine("\n📸 测试 1: Windows 屏幕捕获");
            await TestWindowsScreenCapture();

            // 测试 2: 创建输入模拟服务
            Console.WriteLine("\n🖱️ 测试 2: Windows 输入模拟");
            await TestWindowsInputSimulator();

            // 测试 3: 测试自动化步骤
            Console.WriteLine("\n🔧 测试 3: 自动化步骤");
            await TestAutomationSteps();

            Console.WriteLine("\n✅ 所有测试完成!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
    }

    private static async Task TestWindowsScreenCapture()
    {
        try
        {
            var logger = CreateLogger<WindowsScreenCapture>();
            var screenCapture = new WindowsScreenCapture(logger);

            // 测试屏幕捕获
            var imageData = await screenCapture.CaptureScreenAsync();
            Console.WriteLine($"  ✅ 屏幕捕获成功，图像大小: {imageData.Length} bytes");

            // 测试获取窗口列表
            var windows = await screenCapture.GetVisibleWindowsAsync();
            Console.WriteLine($"  ✅ 获取窗口列表成功，找到 {windows.Count()} 个窗口");

            // 显示前几个窗口
            foreach (var window in windows.Take(3))
            {
                Console.WriteLine($"    - {window.Title} ({window.ProcessName})");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ 屏幕捕获测试失败: {ex.Message}");
        }
    }

    private static async Task TestWindowsInputSimulator()
    {
        try
        {
            var logger = CreateLogger<WindowsInputSimulator>();
            var inputSimulator = new WindowsInputSimulator(logger);

            Console.WriteLine("  ⏳ 将在 2 秒后测试键盘输入...");
            await Task.Delay(2000);

            // 测试键盘输入
            await inputSimulator.SendKeysAsync("Hello AIRobot!");
            Console.WriteLine("  ✅ 键盘输入测试完成");

            await Task.Delay(1000);

            // 测试按键
            await inputSimulator.SendKeyAsync(VirtualKey.Enter);
            Console.WriteLine("  ✅ 按键测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ 输入模拟测试失败: {ex.Message}");
        }
    }

    private static async Task TestAutomationSteps()
    {
        try
        {
            // 创建模拟的自动化上下文
            var mockContext = new MockAutomationContext();

            // 测试点击步骤
            var clickStep = new ClickStep
            {
                Name = "测试点击",
                Position = new Point(100, 100),
                Button = MouseButton.Left
            };

            var clickResult = await clickStep.ExecuteAsync(mockContext);
            Console.WriteLine($"  ✅ 点击步骤测试: {clickResult.IsSuccess} - {clickResult.Message}");

            // 测试文字输入步骤
            var typeStep = new TypeTextStep
            {
                Name = "测试输入",
                Text = "测试文字"
            };

            var typeResult = await typeStep.ExecuteAsync(mockContext);
            Console.WriteLine($"  ✅ 输入步骤测试: {typeResult.IsSuccess} - {typeResult.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ 自动化步骤测试失败: {ex.Message}");
        }
    }

    private static ILogger<T> CreateLogger<T>()
    {
        var loggerFactory = LoggerFactory.Create(builder =>
        {
            builder.AddConsole().SetMinimumLevel(LogLevel.Information);
        });
        return loggerFactory.CreateLogger<T>();
    }
}

/// <summary>
/// 模拟的自动化上下文，用于测试
/// </summary>
public class MockAutomationContext : IAutomationContext
{
    private readonly Dictionary<string, object> _variables = new();

    public T GetService<T>() where T : class
    {
        if (typeof(T) == typeof(IInputSimulator))
        {
            var logger = CreateLogger<WindowsInputSimulator>();
            return new WindowsInputSimulator(logger) as T ?? throw new InvalidOperationException();
        }
        
        throw new NotSupportedException($"Service {typeof(T).Name} not supported in mock context");
    }

    public void SetVariable(string name, object value)
    {
        _variables[name] = value;
    }

    public T? GetVariable<T>(string name)
    {
        if (_variables.TryGetValue(name, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    public bool HasVariable(string name)
    {
        return _variables.ContainsKey(name);
    }

    public void Log(Microsoft.Extensions.Logging.LogLevel level, string message)
    {
        Console.WriteLine($"[{level}] {message}");
    }

    public async Task DelayAsync(TimeSpan delay)
    {
        await Task.Delay(delay);
    }

    private static ILogger<T> CreateLogger<T>()
    {
        var loggerFactory = LoggerFactory.Create(builder =>
        {
            builder.AddConsole().SetMinimumLevel(LogLevel.Information);
        });
        return loggerFactory.CreateLogger<T>();
    }
}
