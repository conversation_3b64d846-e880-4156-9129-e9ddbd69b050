using System.Diagnostics;
using AIRobot.Core.Interfaces;

namespace AIRobot.Core.Models.Steps;

/// <summary>
/// 文字输入步骤
/// </summary>
public class TypeTextStep : AutomationStep
{
    public string Text { get; set; } = string.Empty;
    public bool ClearBefore { get; set; }
    public TimeSpan TypingDelay { get; set; } = TimeSpan.FromMilliseconds(50);
    
    public override async Task<StepResult> ExecuteAsync(IAutomationContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var inputSimulator = context.GetService<IInputSimulator>();
            
            context.Log(Microsoft.Extensions.Logging.LogLevel.Information, 
                $"执行文字输入: '{Text}', 清空前置内容: {ClearBefore}");
            
            if (ClearBefore)
            {
                await inputSimulator.SendKeyComboAsync(VirtualKey.Control, VirtualKey.A);
                await Task.Delay(100);
            }
            
            await inputSimulator.SendKeysAsync(Text);
            
            if (Delay > TimeSpan.Zero)
            {
                await context.DelayAsync(Delay);
            }
            
            return new StepResult
            {
                IsSuccess = true,
                Message = $"成功输入文字: {Text}",
                ExecutionTime = stopwatch.Elapsed
            };
        }
        catch (Exception ex)
        {
            context.Log(Microsoft.Extensions.Logging.LogLevel.Error, 
                $"文字输入失败: {ex.Message}");
            
            return new StepResult
            {
                IsSuccess = false,
                Message = $"文字输入失败: {ex.Message}",
                Exception = ex,
                ExecutionTime = stopwatch.Elapsed
            };
        }
    }
}
