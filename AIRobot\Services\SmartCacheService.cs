using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace AIRobot.Core.Services;

/// <summary>
/// 智能缓存服务
/// 提供多层缓存、自动过期、内存管理等功能
/// </summary>
public class SmartCacheService : IDisposable
{
    private readonly ILogger<SmartCacheService> _logger;
    private readonly ConcurrentDictionary<string, CacheItem> _memoryCache;
    private readonly Timer _cleanupTimer;
    private readonly SemaphoreSlim _cleanupSemaphore;
    private readonly string _diskCacheDirectory;
    private readonly long _maxMemoryCacheSize;
    private readonly TimeSpan _defaultExpiration;
    private long _currentMemoryUsage;
    private bool _disposed = false;

    public SmartCacheService(ILogger<SmartCacheService> logger, 
        long maxMemoryCacheSizeMB = 100, 
        TimeSpan? defaultExpiration = null)
    {
        _logger = logger;
        _memoryCache = new ConcurrentDictionary<string, CacheItem>();
        _cleanupSemaphore = new SemaphoreSlim(1, 1);
        _maxMemoryCacheSize = maxMemoryCacheSizeMB * 1024 * 1024; // 转换为字节
        _defaultExpiration = defaultExpiration ?? TimeSpan.FromMinutes(30);
        
        // 设置磁盘缓存目录
        _diskCacheDirectory = Path.Combine(Path.GetTempPath(), "AIRobot", "Cache");
        Directory.CreateDirectory(_diskCacheDirectory);

        // 启动清理定时器
        _cleanupTimer = new Timer(CleanupExpiredItems, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.LogInformation("智能缓存服务已启动，最大内存缓存: {MaxSize}MB", maxMemoryCacheSizeMB);
    }

    /// <summary>
    /// 获取或创建缓存项
    /// </summary>
    public async Task<T?> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null)
    {
        if (string.IsNullOrEmpty(key))
            throw new ArgumentException("缓存键不能为空", nameof(key));

        var cacheKey = GenerateCacheKey(key);
        
        // 首先尝试从内存缓存获取
        if (_memoryCache.TryGetValue(cacheKey, out var memoryItem) && !memoryItem.IsExpired)
        {
            _logger.LogDebug("从内存缓存命中: {Key}", key);
            return JsonSerializer.Deserialize<T>(memoryItem.Data);
        }

        // 尝试从磁盘缓存获取
        var diskItem = await GetFromDiskCacheAsync<T>(cacheKey);
        if (diskItem != null)
        {
            _logger.LogDebug("从磁盘缓存命中: {Key}", key);
            
            // 将磁盘缓存项提升到内存缓存
            await SetMemoryCacheAsync(cacheKey, diskItem, expiration ?? _defaultExpiration);
            return diskItem;
        }

        // 缓存未命中，执行工厂方法
        _logger.LogDebug("缓存未命中，执行工厂方法: {Key}", key);
        var value = await factory();
        
        if (value != null)
        {
            await SetAsync(key, value, expiration);
        }

        return value;
    }

    /// <summary>
    /// 设置缓存项
    /// </summary>
    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null)
    {
        if (string.IsNullOrEmpty(key))
            throw new ArgumentException("缓存键不能为空", nameof(key));

        var cacheKey = GenerateCacheKey(key);
        var exp = expiration ?? _defaultExpiration;
        
        // 序列化数据
        var jsonData = JsonSerializer.Serialize(value);
        var dataBytes = Encoding.UTF8.GetBytes(jsonData);

        // 根据数据大小决定存储策略
        if (dataBytes.Length > 1024 * 1024) // 大于 1MB 的数据存储到磁盘
        {
            await SetDiskCacheAsync(cacheKey, value, exp);
            _logger.LogDebug("大数据存储到磁盘缓存: {Key}, 大小: {Size}KB", key, dataBytes.Length / 1024);
        }
        else
        {
            await SetMemoryCacheAsync(cacheKey, value, exp);
            _logger.LogDebug("数据存储到内存缓存: {Key}, 大小: {Size}B", key, dataBytes.Length);
        }
    }

    /// <summary>
    /// 获取缓存项
    /// </summary>
    public async Task<T?> GetAsync<T>(string key)
    {
        if (string.IsNullOrEmpty(key))
            return default(T);

        var cacheKey = GenerateCacheKey(key);
        
        // 尝试从内存缓存获取
        if (_memoryCache.TryGetValue(cacheKey, out var memoryItem) && !memoryItem.IsExpired)
        {
            return JsonSerializer.Deserialize<T>(memoryItem.Data);
        }

        // 尝试从磁盘缓存获取
        return await GetFromDiskCacheAsync<T>(cacheKey);
    }

    /// <summary>
    /// 删除缓存项
    /// </summary>
    public async Task RemoveAsync(string key)
    {
        if (string.IsNullOrEmpty(key))
            return;

        var cacheKey = GenerateCacheKey(key);
        
        // 从内存缓存删除
        if (_memoryCache.TryRemove(cacheKey, out var item))
        {
            Interlocked.Add(ref _currentMemoryUsage, -item.Size);
        }

        // 从磁盘缓存删除
        var diskPath = GetDiskCachePath(cacheKey);
        if (File.Exists(diskPath))
        {
            try
            {
                File.Delete(diskPath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "删除磁盘缓存文件失败: {Path}", diskPath);
            }
        }
    }

    /// <summary>
    /// 清空所有缓存
    /// </summary>
    public async Task ClearAllAsync()
    {
        _logger.LogInformation("清空所有缓存");
        
        // 清空内存缓存
        _memoryCache.Clear();
        Interlocked.Exchange(ref _currentMemoryUsage, 0);

        // 清空磁盘缓存
        try
        {
            if (Directory.Exists(_diskCacheDirectory))
            {
                Directory.Delete(_diskCacheDirectory, true);
                Directory.CreateDirectory(_diskCacheDirectory);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清空磁盘缓存失败");
        }
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    public CacheStatistics GetStatistics()
    {
        var diskFiles = Directory.Exists(_diskCacheDirectory) 
            ? Directory.GetFiles(_diskCacheDirectory, "*.cache").Length 
            : 0;

        return new CacheStatistics
        {
            MemoryCacheCount = _memoryCache.Count,
            DiskCacheCount = diskFiles,
            MemoryUsageBytes = _currentMemoryUsage,
            MaxMemoryBytes = _maxMemoryCacheSize,
            MemoryUsagePercentage = (double)_currentMemoryUsage / _maxMemoryCacheSize * 100
        };
    }

    private async Task SetMemoryCacheAsync<T>(string cacheKey, T value, TimeSpan expiration)
    {
        var jsonData = JsonSerializer.Serialize(value);
        var dataBytes = Encoding.UTF8.GetBytes(jsonData);
        
        var cacheItem = new CacheItem
        {
            Data = jsonData,
            ExpirationTime = DateTime.UtcNow.Add(expiration),
            Size = dataBytes.Length,
            LastAccessed = DateTime.UtcNow
        };

        // 检查内存限制
        if (_currentMemoryUsage + cacheItem.Size > _maxMemoryCacheSize)
        {
            await EvictLeastRecentlyUsedAsync(cacheItem.Size);
        }

        _memoryCache.AddOrUpdate(cacheKey, cacheItem, (key, oldValue) =>
        {
            Interlocked.Add(ref _currentMemoryUsage, -oldValue.Size);
            return cacheItem;
        });

        Interlocked.Add(ref _currentMemoryUsage, cacheItem.Size);
    }

    private async Task SetDiskCacheAsync<T>(string cacheKey, T value, TimeSpan expiration)
    {
        var cacheData = new DiskCacheItem<T>
        {
            Value = value,
            ExpirationTime = DateTime.UtcNow.Add(expiration)
        };

        var jsonData = JsonSerializer.Serialize(cacheData);
        var filePath = GetDiskCachePath(cacheKey);

        try
        {
            await File.WriteAllTextAsync(filePath, jsonData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "写入磁盘缓存失败: {Path}", filePath);
        }
    }

    private async Task<T?> GetFromDiskCacheAsync<T>(string cacheKey)
    {
        var filePath = GetDiskCachePath(cacheKey);
        
        if (!File.Exists(filePath))
            return default(T);

        try
        {
            var jsonData = await File.ReadAllTextAsync(filePath);
            var cacheData = JsonSerializer.Deserialize<DiskCacheItem<T>>(jsonData);
            
            if (cacheData != null && cacheData.ExpirationTime > DateTime.UtcNow)
            {
                return cacheData.Value;
            }
            else
            {
                // 过期文件，删除
                File.Delete(filePath);
                return default(T);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "读取磁盘缓存失败: {Path}", filePath);
            return default(T);
        }
    }

    private async Task EvictLeastRecentlyUsedAsync(long requiredSpace)
    {
        var itemsToRemove = _memoryCache.Values
            .OrderBy(item => item.LastAccessed)
            .TakeWhile(item =>
            {
                requiredSpace -= item.Size;
                return requiredSpace > 0;
            })
            .ToList();

        foreach (var item in itemsToRemove)
        {
            var keyToRemove = _memoryCache.FirstOrDefault(kvp => kvp.Value == item).Key;
            if (keyToRemove != null && _memoryCache.TryRemove(keyToRemove, out var removedItem))
            {
                Interlocked.Add(ref _currentMemoryUsage, -removedItem.Size);
            }
        }

        _logger.LogDebug("LRU 淘汰了 {Count} 个缓存项", itemsToRemove.Count);
    }

    private void CleanupExpiredItems(object? state)
    {
        if (!_cleanupSemaphore.Wait(100))
            return;

        try
        {
            // 清理过期的内存缓存项
            var expiredKeys = _memoryCache
                .Where(kvp => kvp.Value.IsExpired)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                if (_memoryCache.TryRemove(key, out var item))
                {
                    Interlocked.Add(ref _currentMemoryUsage, -item.Size);
                }
            }

            // 清理过期的磁盘缓存文件
            if (Directory.Exists(_diskCacheDirectory))
            {
                var cacheFiles = Directory.GetFiles(_diskCacheDirectory, "*.cache");
                foreach (var file in cacheFiles)
                {
                    try
                    {
                        var lastWrite = File.GetLastWriteTimeUtc(file);
                        if (DateTime.UtcNow - lastWrite > _defaultExpiration)
                        {
                            File.Delete(file);
                        }
                    }
                    catch
                    {
                        // 忽略删除错误
                    }
                }
            }

            if (expiredKeys.Any())
            {
                _logger.LogDebug("清理了 {Count} 个过期缓存项", expiredKeys.Count);
            }
        }
        finally
        {
            _cleanupSemaphore.Release();
        }
    }

    private string GenerateCacheKey(string key)
    {
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(key));
        return Convert.ToHexString(hashBytes);
    }

    private string GetDiskCachePath(string cacheKey)
    {
        return Path.Combine(_diskCacheDirectory, $"{cacheKey}.cache");
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _cleanupTimer?.Dispose();
            _cleanupSemaphore?.Dispose();
            _disposed = true;
        }
    }

    private class CacheItem
    {
        public string Data { get; set; } = "";
        public DateTime ExpirationTime { get; set; }
        public long Size { get; set; }
        public DateTime LastAccessed { get; set; }
        public bool IsExpired => DateTime.UtcNow > ExpirationTime;
    }

    private class DiskCacheItem<T>
    {
        public T? Value { get; set; }
        public DateTime ExpirationTime { get; set; }
    }
}

/// <summary>
/// 缓存统计信息
/// </summary>
public class CacheStatistics
{
    public int MemoryCacheCount { get; set; }
    public int DiskCacheCount { get; set; }
    public long MemoryUsageBytes { get; set; }
    public long MaxMemoryBytes { get; set; }
    public double MemoryUsagePercentage { get; set; }
}
