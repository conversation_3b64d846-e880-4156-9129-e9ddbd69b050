using System.Drawing;
using System.Drawing.Imaging;
using Microsoft.Extensions.Logging;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Core.Exceptions;
using System.Diagnostics;
using System.Text;

namespace AIRobot.Platforms.Windows;

/// <summary>
/// Windows 屏幕捕获实现
/// </summary>
public class WindowsScreenCapture : IScreenCapture
{
    private readonly ILogger<WindowsScreenCapture> _logger;

    public WindowsScreenCapture(ILogger<WindowsScreenCapture> logger)
    {
        _logger = logger;
    }

    public async Task<byte[]> CaptureScreenAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                // 获取主屏幕尺寸
                var screenWidth = Win32Api.GetSystemMetrics(0);  // SM_CXSCREEN
                var screenHeight = Win32Api.GetSystemMetrics(1); // SM_CYSCREEN
                var screenBounds = new Rectangle(0, 0, screenWidth, screenHeight);
                _logger.LogDebug("捕获屏幕: {Width}x{Height}", screenBounds.Width, screenBounds.Height);

                using var bitmap = new Bitmap(screenBounds.Width, screenBounds.Height, PixelFormat.Format24bppRgb);
                using var graphics = Graphics.FromImage(bitmap);
                
                graphics.CopyFromScreen(0, 0, 0, 0, screenBounds.Size, CopyPixelOperation.SourceCopy);
                
                using var stream = new MemoryStream();
                bitmap.Save(stream, ImageFormat.Png);
                
                _logger.LogDebug("屏幕捕获完成，图像大小: {Size} bytes", stream.Length);
                return stream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "屏幕捕获失败");
                throw new ScreenCaptureException("屏幕捕获失败", ex);
            }
        });
    }

    public async Task<byte[]> CaptureWindowAsync(IntPtr windowHandle)
    {
        return await Task.Run(() =>
        {
            try
            {
                if (windowHandle == IntPtr.Zero)
                {
                    throw new ArgumentException("无效的窗口句柄");
                }

                if (!Win32Api.GetWindowRect(windowHandle, out var rect))
                {
                    throw new ScreenCaptureException("无法获取窗口边界");
                }

                var width = rect.Right - rect.Left;
                var height = rect.Bottom - rect.Top;

                if (width <= 0 || height <= 0)
                {
                    throw new ScreenCaptureException("窗口大小无效");
                }

                _logger.LogDebug("捕获窗口: Handle={Handle}, Size={Width}x{Height}", 
                    windowHandle, width, height);

                using var bitmap = new Bitmap(width, height, PixelFormat.Format24bppRgb);
                using var graphics = Graphics.FromImage(bitmap);
                
                var hdcSrc = Win32Api.GetDC(windowHandle);
                var hdcDest = graphics.GetHdc();
                
                try
                {
                    Win32Api.BitBlt(hdcDest, 0, 0, width, height, hdcSrc, 0, 0, Win32Api.SRCCOPY);
                }
                finally
                {
                    graphics.ReleaseHdc(hdcDest);
                    Win32Api.ReleaseDC(windowHandle, hdcSrc);
                }
                
                using var stream = new MemoryStream();
                bitmap.Save(stream, ImageFormat.Png);
                
                _logger.LogDebug("窗口捕获完成，图像大小: {Size} bytes", stream.Length);
                return stream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "窗口捕获失败: Handle={Handle}", windowHandle);
                throw new ScreenCaptureException("窗口捕获失败", ex);
            }
        });
    }

    public async Task<byte[]> CaptureRegionAsync(Rectangle region)
    {
        return await Task.Run(() =>
        {
            try
            {
                if (region.Width <= 0 || region.Height <= 0)
                {
                    throw new ArgumentException("区域大小无效");
                }

                _logger.LogDebug("捕获区域: X={X}, Y={Y}, Width={Width}, Height={Height}", 
                    region.X, region.Y, region.Width, region.Height);

                using var bitmap = new Bitmap(region.Width, region.Height, PixelFormat.Format24bppRgb);
                using var graphics = Graphics.FromImage(bitmap);
                
                graphics.CopyFromScreen(region.X, region.Y, 0, 0, region.Size, CopyPixelOperation.SourceCopy);
                
                using var stream = new MemoryStream();
                bitmap.Save(stream, ImageFormat.Png);
                
                _logger.LogDebug("区域捕获完成，图像大小: {Size} bytes", stream.Length);
                return stream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "区域捕获失败: {Region}", region);
                throw new ScreenCaptureException("区域捕获失败", ex);
            }
        });
    }

    public async Task<Rectangle> GetWindowBoundsAsync(IntPtr windowHandle)
    {
        return await Task.Run(() =>
        {
            try
            {
                if (windowHandle == IntPtr.Zero)
                {
                    throw new ArgumentException("无效的窗口句柄");
                }

                if (!Win32Api.GetWindowRect(windowHandle, out var rect))
                {
                    throw new ScreenCaptureException("无法获取窗口边界");
                }

                var bounds = new Rectangle(rect.Left, rect.Top, 
                    rect.Right - rect.Left, rect.Bottom - rect.Top);
                
                _logger.LogDebug("获取窗口边界: Handle={Handle}, Bounds={Bounds}", windowHandle, bounds);
                return bounds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取窗口边界失败: Handle={Handle}", windowHandle);
                throw new ScreenCaptureException("获取窗口边界失败", ex);
            }
        });
    }

    public async Task<IEnumerable<WindowInfo>> GetVisibleWindowsAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                var windows = new List<WindowInfo>();
                
                Win32Api.EnumWindows((hWnd, lParam) =>
                {
                    try
                    {
                        if (Win32Api.IsWindowVisible(hWnd))
                        {
                            var title = GetWindowTitle(hWnd);
                            if (!string.IsNullOrEmpty(title))
                            {
                                Win32Api.GetWindowRect(hWnd, out var rect);
                                Win32Api.GetWindowThreadProcessId(hWnd, out var processId);
                                
                                var processName = "";
                                try
                                {
                                    var process = Process.GetProcessById((int)processId);
                                    processName = process.ProcessName;
                                }
                                catch
                                {
                                    // 忽略无法获取进程名的情况
                                }
                                
                                windows.Add(new WindowInfo
                                {
                                    Handle = hWnd,
                                    Title = title,
                                    ProcessName = processName,
                                    Bounds = new Rectangle(rect.Left, rect.Top, 
                                        rect.Right - rect.Left, rect.Bottom - rect.Top),
                                    IsVisible = true,
                                    IsMinimized = Win32Api.IsIconic(hWnd),
                                    ProcessId = (int)processId
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "枚举窗口时出错: Handle={Handle}", hWnd);
                    }
                    
                    return true; // 继续枚举
                }, IntPtr.Zero);
                
                _logger.LogDebug("找到 {Count} 个可见窗口", windows.Count);
                return windows;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可见窗口列表失败");
                throw new ScreenCaptureException("获取可见窗口列表失败", ex);
            }
        });
    }

    private string GetWindowTitle(IntPtr hWnd)
    {
        try
        {
            var length = Win32Api.GetWindowTextLength(hWnd);
            if (length == 0) return string.Empty;
            
            var title = new StringBuilder(length + 1);
            Win32Api.GetWindowText(hWnd, title, title.Capacity);
            return title.ToString();
        }
        catch
        {
            return string.Empty;
        }
    }
}
