using System;
using System.Drawing;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using AIRobot.Core.Models;
using AIRobot.Platforms.Windows;

namespace AIRobot.QuickTest;

/// <summary>
/// 快速测试程序，验证核心功能
/// </summary>
class QuickTest
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🤖 AIRobot 快速验收测试");
        Console.WriteLine("========================");

        try
        {
            // 创建日志工厂
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole().SetMinimumLevel(LogLevel.Information);
            });

            Console.WriteLine("\n📸 测试 1: Windows 屏幕捕获");
            await TestScreenCapture(loggerFactory);

            Console.WriteLine("\n🖱️ 测试 2: Windows 输入模拟");
            await TestInputSimulation(loggerFactory);

            Console.WriteLine("\n🪟 测试 3: 窗口管理");
            await TestWindowManagement(loggerFactory);

            Console.WriteLine("\n✅ 所有核心功能测试完成!");
            Console.WriteLine("\n🎉 AIRobot 验收测试通过!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }

        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }

    static async Task TestScreenCapture(ILoggerFactory loggerFactory)
    {
        try
        {
            var logger = loggerFactory.CreateLogger<WindowsScreenCapture>();
            var screenCapture = new WindowsScreenCapture(logger);

            // 测试屏幕捕获
            Console.WriteLine("  正在捕获屏幕...");
            var imageData = await screenCapture.CaptureScreenAsync();
            Console.WriteLine($"  ✅ 屏幕捕获成功，图像大小: {imageData.Length:N0} bytes");

            // 保存截图
            var fileName = $"test_screenshot_{DateTime.Now:yyyyMMdd_HHmmss}.png";
            await File.WriteAllBytesAsync(fileName, imageData);
            Console.WriteLine($"  📁 截图已保存: {fileName}");

            // 测试区域捕获
            var region = new Rectangle(100, 100, 300, 200);
            var regionData = await screenCapture.CaptureRegionAsync(region);
            Console.WriteLine($"  ✅ 区域捕获成功，图像大小: {regionData.Length:N0} bytes");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ 屏幕捕获测试失败: {ex.Message}");
            throw;
        }
    }

    static async Task TestInputSimulation(ILoggerFactory loggerFactory)
    {
        try
        {
            var logger = loggerFactory.CreateLogger<WindowsInputSimulator>();
            var inputSimulator = new WindowsInputSimulator(logger);

            Console.WriteLine("  ⏳ 将在 2 秒后测试键盘输入 (请确保有文本输入框处于焦点)...");
            await Task.Delay(2000);

            // 测试键盘输入
            await inputSimulator.SendKeysAsync("AIRobot Test - ");
            Console.WriteLine("  ✅ 文本输入完成");

            await Task.Delay(500);

            // 测试时间戳
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            await inputSimulator.SendKeysAsync(timestamp);
            Console.WriteLine("  ✅ 时间戳输入完成");

            await Task.Delay(500);

            // 测试按键
            await inputSimulator.SendKeyAsync(VirtualKey.Enter);
            Console.WriteLine("  ✅ 回车键测试完成");

            await Task.Delay(500);

            // 测试组合键
            await inputSimulator.SendKeyComboAsync(VirtualKey.Control, VirtualKey.A);
            Console.WriteLine("  ✅ 组合键 (Ctrl+A) 测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ 输入模拟测试失败: {ex.Message}");
            throw;
        }
    }

    static async Task TestWindowManagement(ILoggerFactory loggerFactory)
    {
        try
        {
            var logger = loggerFactory.CreateLogger<WindowsScreenCapture>();
            var screenCapture = new WindowsScreenCapture(logger);

            Console.WriteLine("  正在获取窗口列表...");
            var windows = await screenCapture.GetVisibleWindowsAsync();
            
            Console.WriteLine($"  ✅ 找到 {windows.Count()} 个可见窗口");

            // 显示前10个窗口
            var topWindows = windows.Take(10).ToList();
            foreach (var window in topWindows)
            {
                Console.WriteLine($"    - {window.Title} ({window.ProcessName}) [{window.Bounds.Width}x{window.Bounds.Height}]");
            }

            if (windows.Count() > 10)
            {
                Console.WriteLine($"    ... 还有 {windows.Count() - 10} 个窗口");
            }

            // 测试查找特定窗口
            var notepadWindows = windows.Where(w => 
                w.ProcessName.Contains("notepad", StringComparison.OrdinalIgnoreCase) ||
                w.Title.Contains("记事本", StringComparison.OrdinalIgnoreCase)).ToList();

            if (notepadWindows.Any())
            {
                Console.WriteLine($"  ✅ 找到记事本窗口: {notepadWindows.Count} 个");
            }
            else
            {
                Console.WriteLine("  ℹ️ 未找到记事本窗口 (这是正常的)");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ 窗口管理测试失败: {ex.Message}");
            throw;
        }
    }
}
