using System.Drawing;

namespace AIRobot.Core.Models;

/// <summary>
/// OCR 引擎类型
/// </summary>
public enum OcrEngine
{
    Auto,           // 自动选择最佳引擎
    Tesseract,      // 传统 OCR，兼容性好
    PaddleOCR,      // 高精度中英文识别
    Surya,          // 专业公式和表格识别
    EasyOCR         // 备用方案
}

/// <summary>
/// OCR 识别结果
/// </summary>
public class OcrResult
{
    public string Text { get; set; } = string.Empty;
    public float Confidence { get; set; }
    public IEnumerable<TextRegion> Regions { get; set; } = new List<TextRegion>();
    public TimeSpan ProcessingTime { get; set; }
    public OcrEngine UsedEngine { get; set; }
    public string Language { get; set; } = "auto";
}

/// <summary>
/// 文字区域
/// </summary>
public class TextRegion
{
    public string Text { get; set; } = string.Empty;
    public Rectangle Bounds { get; set; }
    public float Confidence { get; set; }
    public int LineNumber { get; set; }
    public int WordIndex { get; set; }
    public IEnumerable<CharacterInfo> Characters { get; set; } = new List<CharacterInfo>();
}

/// <summary>
/// 字符信息
/// </summary>
public class CharacterInfo
{
    public char Character { get; set; }
    public Rectangle Bounds { get; set; }
    public float Confidence { get; set; }
}

/// <summary>
/// 公式识别结果
/// </summary>
public class FormulaResult
{
    public string LaTeX { get; set; } = string.Empty;
    public string MathML { get; set; } = string.Empty;
    public Rectangle Bounds { get; set; }
    public float Confidence { get; set; }
    public FormulaType Type { get; set; }
}

/// <summary>
/// 公式类型
/// </summary>
public enum FormulaType
{
    Inline,         // 行内公式
    Display,        // 独立公式
    Equation,       // 方程式
    Matrix,         // 矩阵
    Chemical        // 化学公式
}

/// <summary>
/// 表格识别结果
/// </summary>
public class TableResult
{
    public int Rows { get; set; }
    public int Columns { get; set; }
    public IEnumerable<TableCell> Cells { get; set; } = new List<TableCell>();
    public Rectangle Bounds { get; set; }
    public float Confidence { get; set; }
}

/// <summary>
/// 表格单元格
/// </summary>
public class TableCell
{
    public string Text { get; set; } = string.Empty;
    public int Row { get; set; }
    public int Column { get; set; }
    public Rectangle Bounds { get; set; }
    public int RowSpan { get; set; } = 1;
    public int ColumnSpan { get; set; } = 1;
}

/// <summary>
/// 图像分析结果
/// </summary>
public class ImageAnalysis
{
    public bool ContainsMathFormula { get; set; }
    public string PrimaryLanguage { get; set; } = "auto";
    public bool ContainsTable { get; set; }
    public float TextDensity { get; set; }
    public Size ImageSize { get; set; }
}
