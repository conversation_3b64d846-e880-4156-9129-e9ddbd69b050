/* Bootstrap CSS - 这里应该包含完整的Bootstrap CSS */
/* 为了简化，我们使用CDN版本，这个文件可以为空或包含自定义的Bootstrap样式 */

/* 自定义样式 */
.card {
    margin-bottom: 1rem;
}

.btn-group-vertical .btn {
    margin-bottom: 0.25rem;
}

.list-group-item {
    border: 1px solid rgba(0,0,0,.125);
}

.badge {
    font-size: 0.75em;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

.text-muted {
    color: #6c757d !important;
}

.alert {
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.form-select {
    display: block;
    width: 100%;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}
