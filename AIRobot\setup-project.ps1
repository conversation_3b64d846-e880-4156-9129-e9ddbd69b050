# AIRobot 项目初始化脚本
# PowerShell 脚本用于快速搭建项目结构

Write-Host "开始初始化 AIRobot 项目..." -ForegroundColor Green

# 检查 .NET 9 SDK
Write-Host "检查 .NET 9 SDK..." -ForegroundColor Yellow
$dotnetVersion = dotnet --version
if ($dotnetVersion -notmatch "^9\.") {
    Write-Host "错误: 需要安装 .NET 9 SDK" -ForegroundColor Red
    Write-Host "请从 https://dotnet.microsoft.com/download 下载并安装" -ForegroundColor Red
    exit 1
}
Write-Host "✓ .NET SDK 版本: $dotnetVersion" -ForegroundColor Green

# 检查 MAUI 工作负载
Write-Host "检查 MAUI 工作负载..." -ForegroundColor Yellow
$workloads = dotnet workload list
if ($workloads -notmatch "maui") {
    Write-Host "安装 MAUI 工作负载..." -ForegroundColor Yellow
    dotnet workload install maui
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: MAUI 工作负载安装失败" -ForegroundColor Red
        exit 1
    }
}
Write-Host "✓ MAUI 工作负载已安装" -ForegroundColor Green

# 创建解决方案
Write-Host "创建解决方案..." -ForegroundColor Yellow
if (Test-Path "AIRobot.sln") {
    Write-Host "解决方案文件已存在，跳过创建" -ForegroundColor Yellow
} else {
    dotnet new sln -n AIRobot
    Write-Host "✓ 解决方案创建完成" -ForegroundColor Green
}

# 创建目录结构
Write-Host "创建目录结构..." -ForegroundColor Yellow
$directories = @(
    "src",
    "src/AIRobot.Core",
    "src/AIRobot.Platforms", 
    "src/AIRobot.MAUI",
    "src/AIRobot.Shared",
    "tests",
    "tests/AIRobot.Core.Tests",
    "tests/AIRobot.Platforms.Tests",
    "docs",
    "samples"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ 创建目录: $dir" -ForegroundColor Green
    }
}

# 创建核心类库
Write-Host "创建核心类库..." -ForegroundColor Yellow
if (!(Test-Path "src/AIRobot.Core/AIRobot.Core.csproj")) {
    Set-Location "src/AIRobot.Core"
    dotnet new classlib -n AIRobot.Core --force
    Set-Location "../.."
    dotnet sln add src/AIRobot.Core/AIRobot.Core.csproj
    Write-Host "✓ 核心类库创建完成" -ForegroundColor Green
}

# 创建平台实现类库
Write-Host "创建平台实现类库..." -ForegroundColor Yellow
if (!(Test-Path "src/AIRobot.Platforms/AIRobot.Platforms.csproj")) {
    Set-Location "src/AIRobot.Platforms"
    dotnet new classlib -n AIRobot.Platforms --force
    Set-Location "../.."
    dotnet sln add src/AIRobot.Platforms/AIRobot.Platforms.csproj
    Write-Host "✓ 平台实现类库创建完成" -ForegroundColor Green
}

# 创建 MAUI 项目
Write-Host "创建 MAUI 项目..." -ForegroundColor Yellow
if (!(Test-Path "src/AIRobot.MAUI/AIRobot.MAUI.csproj")) {
    Set-Location "src/AIRobot.MAUI"
    dotnet new maui-blazor -n AIRobot.MAUI --force
    Set-Location "../.."
    dotnet sln add src/AIRobot.MAUI/AIRobot.MAUI.csproj
    Write-Host "✓ MAUI 项目创建完成" -ForegroundColor Green
}

# 创建共享组件库
Write-Host "创建共享组件库..." -ForegroundColor Yellow
if (!(Test-Path "src/AIRobot.Shared/AIRobot.Shared.csproj")) {
    Set-Location "src/AIRobot.Shared"
    dotnet new razorclasslib -n AIRobot.Shared --force
    Set-Location "../.."
    dotnet sln add src/AIRobot.Shared/AIRobot.Shared.csproj
    Write-Host "✓ 共享组件库创建完成" -ForegroundColor Green
}

# 创建测试项目
Write-Host "创建测试项目..." -ForegroundColor Yellow
if (!(Test-Path "tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj")) {
    Set-Location "tests/AIRobot.Core.Tests"
    dotnet new xunit -n AIRobot.Core.Tests --force
    Set-Location "../.."
    dotnet sln add tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj
    Write-Host "✓ 核心测试项目创建完成" -ForegroundColor Green
}

if (!(Test-Path "tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj")) {
    Set-Location "tests/AIRobot.Platforms.Tests"
    dotnet new xunit -n AIRobot.Platforms.Tests --force
    Set-Location "../.."
    dotnet sln add tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj
    Write-Host "✓ 平台测试项目创建完成" -ForegroundColor Green
}

# 添加项目引用
Write-Host "配置项目引用..." -ForegroundColor Yellow

# AIRobot.Platforms 引用 AIRobot.Core
dotnet add src/AIRobot.Platforms/AIRobot.Platforms.csproj reference src/AIRobot.Core/AIRobot.Core.csproj

# AIRobot.MAUI 引用其他项目
dotnet add src/AIRobot.MAUI/AIRobot.MAUI.csproj reference src/AIRobot.Core/AIRobot.Core.csproj
dotnet add src/AIRobot.MAUI/AIRobot.MAUI.csproj reference src/AIRobot.Platforms/AIRobot.Platforms.csproj
dotnet add src/AIRobot.MAUI/AIRobot.MAUI.csproj reference src/AIRobot.Shared/AIRobot.Shared.csproj

# 测试项目引用
dotnet add tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj reference src/AIRobot.Core/AIRobot.Core.csproj
dotnet add tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj reference src/AIRobot.Platforms/AIRobot.Platforms.csproj

Write-Host "✓ 项目引用配置完成" -ForegroundColor Green

# 添加 NuGet 包
Write-Host "添加 NuGet 包..." -ForegroundColor Yellow

# 核心项目包
dotnet add src/AIRobot.Core/AIRobot.Core.csproj package Microsoft.Extensions.DependencyInjection.Abstractions
dotnet add src/AIRobot.Core/AIRobot.Core.csproj package Microsoft.Extensions.Logging.Abstractions
dotnet add src/AIRobot.Core/AIRobot.Core.csproj package System.Drawing.Common

# 平台项目包
dotnet add src/AIRobot.Platforms/AIRobot.Platforms.csproj package Tesseract
dotnet add src/AIRobot.Platforms/AIRobot.Platforms.csproj package SixLabors.ImageSharp
dotnet add src/AIRobot.Platforms/AIRobot.Platforms.csproj package System.Drawing.Common

# MAUI 项目包
dotnet add src/AIRobot.MAUI/AIRobot.MAUI.csproj package CommunityToolkit.Maui

# 测试项目包
dotnet add tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj package Moq
dotnet add tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj package FluentAssertions
dotnet add tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj package Moq
dotnet add tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj package FluentAssertions

Write-Host "✓ NuGet 包添加完成" -ForegroundColor Green

# 还原依赖
Write-Host "还原项目依赖..." -ForegroundColor Yellow
dotnet restore
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 依赖还原完成" -ForegroundColor Green
} else {
    Write-Host "警告: 依赖还原可能存在问题" -ForegroundColor Yellow
}

# 构建解决方案
Write-Host "构建解决方案..." -ForegroundColor Yellow
dotnet build --no-restore
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 解决方案构建成功" -ForegroundColor Green
} else {
    Write-Host "警告: 解决方案构建可能存在问题" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 AIRobot 项目初始化完成!" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Cyan
Write-Host "1. 查看文档: docs/Architecture.md" -ForegroundColor White
Write-Host "2. 查看开发指南: docs/Development-Guide.md" -ForegroundColor White
Write-Host "3. 查看代码示例: docs/Code-Examples.md" -ForegroundColor White
Write-Host "4. 开始开发: 在 Visual Studio 中打开 AIRobot.sln" -ForegroundColor White
Write-Host ""
Write-Host "运行测试: dotnet test" -ForegroundColor Yellow
Write-Host "运行 MAUI 应用: dotnet run --project src/AIRobot.MAUI" -ForegroundColor Yellow
