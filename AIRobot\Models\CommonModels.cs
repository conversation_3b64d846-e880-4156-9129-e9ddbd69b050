using System.Drawing;

namespace AIRobot.Core.Models;

/// <summary>
/// 窗口信息
/// </summary>
public class WindowInfo
{
    public IntPtr Handle { get; set; }
    public string Title { get; set; } = string.Empty;
    public string ProcessName { get; set; } = string.Empty;
    public Rectangle Bounds { get; set; }
    public bool IsVisible { get; set; }
    public bool IsMinimized { get; set; }
    public int ProcessId { get; set; }
}

/// <summary>
/// 鼠标按钮
/// </summary>
public enum MouseButton
{
    Left,
    Right,
    Middle
}

/// <summary>
/// 虚拟按键
/// </summary>
public enum VirtualKey
{
    // 字母键
    A = 0x41, B = 0x42, C = 0x43, D = 0x44, E = 0x45, F = 0x46, G = 0x47,
    H = 0x48, I = 0x49, J = 0x4A, K = 0x4B, L = 0x4C, M = 0x4D, N = 0x4E,
    O = 0x4F, P = 0x50, Q = 0x51, R = 0x52, S = 0x53, T = 0x54, U = 0x55,
    V = 0x56, W = 0x57, X = 0x58, Y = 0x59, Z = 0x5A,
    
    // 数字键
    D0 = 0x30, D1 = 0x31, D2 = 0x32, D3 = 0x33, D4 = 0x34,
    D5 = 0x35, D6 = 0x36, D7 = 0x37, D8 = 0x38, D9 = 0x39,
    
    // 功能键
    F1 = 0x70, F2 = 0x71, F3 = 0x72, F4 = 0x73, F5 = 0x74, F6 = 0x75,
    F7 = 0x76, F8 = 0x77, F9 = 0x78, F10 = 0x79, F11 = 0x7A, F12 = 0x7B,
    
    // 控制键
    Control = 0x11,
    Alt = 0x12,
    Shift = 0x10,
    Tab = 0x09,
    Enter = 0x0D,
    Escape = 0x1B,
    Space = 0x20,
    Backspace = 0x08,
    Delete = 0x2E,
    
    // 方向键
    Left = 0x25,
    Up = 0x26,
    Right = 0x27,
    Down = 0x28,
    
    // 其他常用键
    Home = 0x24,
    End = 0x23,
    PageUp = 0x21,
    PageDown = 0x22,
    Insert = 0x2D
}

/// <summary>
/// 图像处理选项
/// </summary>
public class ImageProcessingOptions
{
    public bool GrayScale { get; set; }
    public bool GaussianBlur { get; set; }
    public int BlurKernelSize { get; set; } = 5;
    public bool EdgeDetection { get; set; }
    public double EdgeThreshold1 { get; set; } = 100;
    public double EdgeThreshold2 { get; set; } = 200;
    public bool Resize { get; set; }
    public Size? TargetSize { get; set; }
}

/// <summary>
/// 轮廓信息
/// </summary>
public class ContourInfo
{
    public Point[] Points { get; set; } = Array.Empty<Point>();
    public Rectangle BoundingRect { get; set; }
    public double Area { get; set; }
    public double Perimeter { get; set; }
}

/// <summary>
/// 模板匹配结果
/// </summary>
public class TemplateMatchResult
{
    public Point Location { get; set; }
    public double Confidence { get; set; }
    public Rectangle MatchRect { get; set; }
    public bool IsMatch { get; set; }
}

/// <summary>
/// 分割结果
/// </summary>
public class SegmentationResult
{
    public byte[] Mask { get; set; } = Array.Empty<byte>();
    public Rectangle BoundingBox { get; set; }
    public float Confidence { get; set; }
}

/// <summary>
/// 对象分割
/// </summary>
public class ObjectSegment
{
    public byte[] Mask { get; set; } = Array.Empty<byte>();
    public Rectangle BoundingBox { get; set; }
    public float Confidence { get; set; }
    public string Label { get; set; } = string.Empty;
}
