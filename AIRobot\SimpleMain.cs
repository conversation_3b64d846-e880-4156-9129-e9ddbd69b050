using System;
using System.Threading.Tasks;
using AIRobot.SimpleTest;

namespace AIRobot;

/// <summary>
/// 简单的主程序入口
/// </summary>
public class SimpleMain
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🤖 AIRobot 简单测试程序");
        Console.WriteLine("========================");
        
        try
        {
            await SimpleTest.RunTestsAsync();
            
            Console.WriteLine("\n✅ 测试完成! 按任意键退出...");
            Console.ReadKey();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ 程序异常: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
