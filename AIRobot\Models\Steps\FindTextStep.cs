using System.Diagnostics;
using System.Drawing;
using AIRobot.Core.Interfaces;

namespace AIRobot.Core.Models.Steps;

/// <summary>
/// OCR 查找文字步骤
/// </summary>
public class FindTextStep : AutomationStep
{
    public string SearchText { get; set; } = string.Empty;
    public Rectangle? SearchRegion { get; set; }
    public float MinConfidence { get; set; } = 0.8f;
    public bool ClickIfFound { get; set; }
    public OcrEngine PreferredEngine { get; set; } = OcrEngine.Auto;
    
    public override async Task<StepResult> ExecuteAsync(IAutomationContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var screenCapture = context.GetService<IScreenCapture>();
            var ocrService = context.GetService<IOcrService>();
            
            context.Log(Microsoft.Extensions.Logging.LogLevel.Information, 
                $"查找文字: '{SearchText}', 最小置信度: {MinConfidence}");
            
            byte[] imageData;
            if (SearchRegion.HasValue)
            {
                imageData = await screenCapture.CaptureRegionAsync(SearchRegion.Value);
            }
            else
            {
                imageData = await screenCapture.CaptureScreenAsync();
            }
            
            var textRegions = await ocrService.FindTextAsync(imageData, SearchText);
            var validRegions = textRegions.Where(r => r.Confidence >= MinConfidence).ToList();
            
            if (validRegions.Any())
            {
                var result = new StepResult
                {
                    IsSuccess = true,
                    Message = $"找到文字 '{SearchText}', 共 {validRegions.Count} 个匹配",
                    ExecutionTime = stopwatch.Elapsed
                };
                
                result.Data["FoundRegions"] = validRegions;
                
                if (ClickIfFound)
                {
                    var firstRegion = validRegions.First();
                    var clickPoint = new Point(
                        firstRegion.Bounds.X + firstRegion.Bounds.Width / 2,
                        firstRegion.Bounds.Y + firstRegion.Bounds.Height / 2
                    );
                    
                    var inputSimulator = context.GetService<IInputSimulator>();
                    await inputSimulator.ClickAsync(clickPoint);
                    
                    result.Message += $", 已点击位置 ({clickPoint.X}, {clickPoint.Y})";
                    
                    context.Log(Microsoft.Extensions.Logging.LogLevel.Information, 
                        $"已点击找到的文字位置: ({clickPoint.X}, {clickPoint.Y})");
                }
                
                return result;
            }
            else
            {
                return new StepResult
                {
                    IsSuccess = false,
                    Message = $"未找到文字 '{SearchText}'",
                    ExecutionTime = stopwatch.Elapsed
                };
            }
        }
        catch (Exception ex)
        {
            context.Log(Microsoft.Extensions.Logging.LogLevel.Error, 
                $"文字查找失败: {ex.Message}");
            
            return new StepResult
            {
                IsSuccess = false,
                Message = $"文字查找失败: {ex.Message}",
                Exception = ex,
                ExecutionTime = stopwatch.Elapsed
            };
        }
    }
}
