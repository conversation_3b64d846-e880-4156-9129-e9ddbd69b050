using System.Drawing;
using System.Runtime.InteropServices;
using AIRobot.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace AIRobot.Platforms.macOS;

/// <summary>
/// macOS平台输入模拟实现
/// 使用CGEvent API进行鼠标和键盘模拟
/// </summary>
public class MacOsInputSimulator : IInputSimulator
{
    private readonly ILogger<MacOsInputSimulator> _logger;

    public MacOsInputSimulator(ILogger<MacOsInputSimulator> logger)
    {
        _logger = logger;
    }

    public async Task ClickAsync(Point position)
    {
        try
        {
            _logger.LogInformation($"macOS鼠标点击: {position}");

            var cgPoint = new CGPoint(position.X, position.Y);
            
            // 创建鼠标按下事件
            var mouseDownEvent = CGEventCreateMouseEvent(IntPtr.Zero, kCGEventLeftMouseDown, cgPoint, kCGMouseButtonLeft);
            if (mouseDownEvent == IntPtr.Zero)
            {
                throw new InvalidOperationException("无法创建鼠标按下事件");
            }

            // 创建鼠标释放事件
            var mouseUpEvent = CGEventCreateMouseEvent(IntPtr.Zero, kCGEventLeftMouseUp, cgPoint, kCGMouseButtonLeft);
            if (mouseUpEvent == IntPtr.Zero)
            {
                CFRelease(mouseDownEvent);
                throw new InvalidOperationException("无法创建鼠标释放事件");
            }

            try
            {
                // 发送事件
                CGEventPost(kCGHIDEventTap, mouseDownEvent);
                await Task.Delay(50); // 短暂延迟
                CGEventPost(kCGHIDEventTap, mouseUpEvent);
                
                _logger.LogInformation($"macOS鼠标点击完成: {position}");
            }
            finally
            {
                CFRelease(mouseDownEvent);
                CFRelease(mouseUpEvent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"macOS鼠标点击失败: {position}");
            throw;
        }
    }

    public async Task DoubleClickAsync(Point position)
    {
        try
        {
            _logger.LogInformation($"macOS鼠标双击: {position}");

            // 执行两次快速点击
            await ClickAsync(position);
            await Task.Delay(100);
            await ClickAsync(position);
            
            _logger.LogInformation($"macOS鼠标双击完成: {position}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"macOS鼠标双击失败: {position}");
            throw;
        }
    }

    public async Task RightClickAsync(Point position)
    {
        try
        {
            _logger.LogInformation($"macOS鼠标右键点击: {position}");

            var cgPoint = new CGPoint(position.X, position.Y);
            
            var mouseDownEvent = CGEventCreateMouseEvent(IntPtr.Zero, kCGEventRightMouseDown, cgPoint, kCGMouseButtonRight);
            var mouseUpEvent = CGEventCreateMouseEvent(IntPtr.Zero, kCGEventRightMouseUp, cgPoint, kCGMouseButtonRight);

            if (mouseDownEvent == IntPtr.Zero || mouseUpEvent == IntPtr.Zero)
            {
                if (mouseDownEvent != IntPtr.Zero) CFRelease(mouseDownEvent);
                if (mouseUpEvent != IntPtr.Zero) CFRelease(mouseUpEvent);
                throw new InvalidOperationException("无法创建右键点击事件");
            }

            try
            {
                CGEventPost(kCGHIDEventTap, mouseDownEvent);
                await Task.Delay(50);
                CGEventPost(kCGHIDEventTap, mouseUpEvent);
                
                _logger.LogInformation($"macOS鼠标右键点击完成: {position}");
            }
            finally
            {
                CFRelease(mouseDownEvent);
                CFRelease(mouseUpEvent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"macOS鼠标右键点击失败: {position}");
            throw;
        }
    }

    public async Task DragAsync(Point startPosition, Point endPosition)
    {
        try
        {
            _logger.LogInformation($"macOS鼠标拖拽: {startPosition} -> {endPosition}");

            var startPoint = new CGPoint(startPosition.X, startPosition.Y);
            var endPoint = new CGPoint(endPosition.X, endPosition.Y);

            // 鼠标按下
            var mouseDownEvent = CGEventCreateMouseEvent(IntPtr.Zero, kCGEventLeftMouseDown, startPoint, kCGMouseButtonLeft);
            CGEventPost(kCGHIDEventTap, mouseDownEvent);
            CFRelease(mouseDownEvent);

            await Task.Delay(100);

            // 鼠标拖拽
            var mouseDragEvent = CGEventCreateMouseEvent(IntPtr.Zero, kCGEventLeftMouseDragged, endPoint, kCGMouseButtonLeft);
            CGEventPost(kCGHIDEventTap, mouseDragEvent);
            CFRelease(mouseDragEvent);

            await Task.Delay(100);

            // 鼠标释放
            var mouseUpEvent = CGEventCreateMouseEvent(IntPtr.Zero, kCGEventLeftMouseUp, endPoint, kCGMouseButtonLeft);
            CGEventPost(kCGHIDEventTap, mouseUpEvent);
            CFRelease(mouseUpEvent);

            _logger.LogInformation($"macOS鼠标拖拽完成: {startPosition} -> {endPosition}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"macOS鼠标拖拽失败: {startPosition} -> {endPosition}");
            throw;
        }
    }

    public async Task ScrollAsync(Point position, int deltaX, int deltaY)
    {
        try
        {
            _logger.LogInformation($"macOS鼠标滚动: {position}, deltaX={deltaX}, deltaY={deltaY}");

            var cgPoint = new CGPoint(position.X, position.Y);
            var scrollEvent = CGEventCreateScrollWheelEvent(IntPtr.Zero, kCGScrollEventUnitPixel, 2, deltaY, deltaX);
            
            if (scrollEvent == IntPtr.Zero)
            {
                throw new InvalidOperationException("无法创建滚动事件");
            }

            try
            {
                CGEventSetLocation(scrollEvent, cgPoint);
                CGEventPost(kCGHIDEventTap, scrollEvent);
                
                _logger.LogInformation($"macOS鼠标滚动完成: {position}");
            }
            finally
            {
                CFRelease(scrollEvent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"macOS鼠标滚动失败: {position}");
            throw;
        }
    }

    public async Task TypeTextAsync(string text)
    {
        try
        {
            _logger.LogInformation($"macOS输入文字: {text.Length} 个字符");

            foreach (char c in text)
            {
                await TypeCharacterAsync(c);
                await Task.Delay(10); // 短暂延迟以模拟真实输入
            }

            _logger.LogInformation($"macOS文字输入完成: {text.Length} 个字符");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"macOS文字输入失败: {text}");
            throw;
        }
    }

    public async Task SendKeyAsync(string key)
    {
        try
        {
            _logger.LogInformation($"macOS发送按键: {key}");

            var keyCode = GetKeyCode(key);
            if (keyCode == -1)
            {
                throw new ArgumentException($"不支持的按键: {key}");
            }

            var keyDownEvent = CGEventCreateKeyboardEvent(IntPtr.Zero, (ushort)keyCode, true);
            var keyUpEvent = CGEventCreateKeyboardEvent(IntPtr.Zero, (ushort)keyCode, false);

            if (keyDownEvent == IntPtr.Zero || keyUpEvent == IntPtr.Zero)
            {
                if (keyDownEvent != IntPtr.Zero) CFRelease(keyDownEvent);
                if (keyUpEvent != IntPtr.Zero) CFRelease(keyUpEvent);
                throw new InvalidOperationException("无法创建键盘事件");
            }

            try
            {
                CGEventPost(kCGHIDEventTap, keyDownEvent);
                await Task.Delay(50);
                CGEventPost(kCGHIDEventTap, keyUpEvent);
                
                _logger.LogInformation($"macOS按键发送完成: {key}");
            }
            finally
            {
                CFRelease(keyDownEvent);
                CFRelease(keyUpEvent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"macOS按键发送失败: {key}");
            throw;
        }
    }

    public async Task SendKeyComboAsync(params string[] keys)
    {
        try
        {
            _logger.LogInformation($"macOS发送组合键: {string.Join("+", keys)}");

            var keyCodes = keys.Select(GetKeyCode).Where(k => k != -1).ToArray();
            if (keyCodes.Length != keys.Length)
            {
                throw new ArgumentException("包含不支持的按键");
            }

            var keyDownEvents = new List<IntPtr>();
            var keyUpEvents = new List<IntPtr>();

            try
            {
                // 创建所有按键事件
                foreach (var keyCode in keyCodes)
                {
                    var keyDownEvent = CGEventCreateKeyboardEvent(IntPtr.Zero, (ushort)keyCode, true);
                    var keyUpEvent = CGEventCreateKeyboardEvent(IntPtr.Zero, (ushort)keyCode, false);
                    
                    if (keyDownEvent != IntPtr.Zero && keyUpEvent != IntPtr.Zero)
                    {
                        keyDownEvents.Add(keyDownEvent);
                        keyUpEvents.Add(keyUpEvent);
                    }
                }

                // 按下所有按键
                foreach (var keyDownEvent in keyDownEvents)
                {
                    CGEventPost(kCGHIDEventTap, keyDownEvent);
                    await Task.Delay(10);
                }

                await Task.Delay(50);

                // 释放所有按键（逆序）
                for (int i = keyUpEvents.Count - 1; i >= 0; i--)
                {
                    CGEventPost(kCGHIDEventTap, keyUpEvents[i]);
                    await Task.Delay(10);
                }

                _logger.LogInformation($"macOS组合键发送完成: {string.Join("+", keys)}");
            }
            finally
            {
                // 清理事件
                foreach (var evt in keyDownEvents.Concat(keyUpEvents))
                {
                    CFRelease(evt);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"macOS组合键发送失败: {string.Join("+", keys)}");
            throw;
        }
    }

    private async Task TypeCharacterAsync(char character)
    {
        var keyCode = GetCharacterKeyCode(character);
        if (keyCode != -1)
        {
            var keyDownEvent = CGEventCreateKeyboardEvent(IntPtr.Zero, (ushort)keyCode, true);
            var keyUpEvent = CGEventCreateKeyboardEvent(IntPtr.Zero, (ushort)keyCode, false);

            if (keyDownEvent != IntPtr.Zero && keyUpEvent != IntPtr.Zero)
            {
                try
                {
                    CGEventPost(kCGHIDEventTap, keyDownEvent);
                    await Task.Delay(10);
                    CGEventPost(kCGHIDEventTap, keyUpEvent);
                }
                finally
                {
                    CFRelease(keyDownEvent);
                    CFRelease(keyUpEvent);
                }
            }
        }
        else
        {
            // 对于无法直接映射的字符，使用Unicode输入
            await TypeUnicodeCharacterAsync(character);
        }
    }

    private async Task TypeUnicodeCharacterAsync(char character)
    {
        var keyboardEvent = CGEventCreateKeyboardEvent(IntPtr.Zero, 0, true);
        if (keyboardEvent != IntPtr.Zero)
        {
            try
            {
                CGEventKeyboardSetUnicodeString(keyboardEvent, 1, new ushort[] { character });
                CGEventPost(kCGHIDEventTap, keyboardEvent);
            }
            finally
            {
                CFRelease(keyboardEvent);
            }
        }
    }

    private int GetKeyCode(string key)
    {
        return key.ToUpper() switch
        {
            "A" => 0, "B" => 11, "C" => 8, "D" => 2, "E" => 14, "F" => 3, "G" => 5, "H" => 4,
            "I" => 34, "J" => 38, "K" => 40, "L" => 37, "M" => 46, "N" => 45, "O" => 31, "P" => 35,
            "Q" => 12, "R" => 15, "S" => 1, "T" => 17, "U" => 32, "V" => 9, "W" => 13, "X" => 7,
            "Y" => 16, "Z" => 6,
            "0" => 29, "1" => 18, "2" => 19, "3" => 20, "4" => 21, "5" => 23, "6" => 22, "7" => 26,
            "8" => 28, "9" => 25,
            "SPACE" => 49, "ENTER" => 36, "TAB" => 48, "ESCAPE" => 53, "BACKSPACE" => 51,
            "DELETE" => 117, "LEFT" => 123, "RIGHT" => 124, "UP" => 126, "DOWN" => 125,
            "CMD" => 55, "COMMAND" => 55, "CTRL" => 59, "CONTROL" => 59, "ALT" => 58, "OPTION" => 58,
            "SHIFT" => 56, "CAPSLOCK" => 57, "FN" => 63,
            _ => -1
        };
    }

    private int GetCharacterKeyCode(char character)
    {
        return character switch
        {
            >= 'a' and <= 'z' => character - 'a' + 0,
            >= 'A' and <= 'Z' => character - 'A' + 0,
            >= '0' and <= '9' => character - '0' + 18,
            ' ' => 49,
            _ => -1
        };
    }

    #region Core Graphics API

    [DllImport("/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics")]
    private static extern IntPtr CGEventCreateMouseEvent(IntPtr source, uint mouseType, CGPoint mouseCursorPosition, uint mouseButton);

    [DllImport("/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics")]
    private static extern IntPtr CGEventCreateKeyboardEvent(IntPtr source, ushort virtualKey, bool keyDown);

    [DllImport("/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics")]
    private static extern IntPtr CGEventCreateScrollWheelEvent(IntPtr source, uint units, uint wheelCount, int wheel1, int wheel2);

    [DllImport("/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics")]
    private static extern void CGEventPost(uint tap, IntPtr eventRef);

    [DllImport("/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics")]
    private static extern void CGEventSetLocation(IntPtr eventRef, CGPoint location);

    [DllImport("/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics")]
    private static extern void CGEventKeyboardSetUnicodeString(IntPtr eventRef, int stringLength, ushort[] unicodeString);

    [DllImport("/System/Library/Frameworks/CoreFoundation.framework/CoreFoundation")]
    private static extern void CFRelease(IntPtr obj);

    private const uint kCGHIDEventTap = 0;
    private const uint kCGEventLeftMouseDown = 1;
    private const uint kCGEventLeftMouseUp = 2;
    private const uint kCGEventRightMouseDown = 3;
    private const uint kCGEventRightMouseUp = 4;
    private const uint kCGEventLeftMouseDragged = 6;
    private const uint kCGScrollEventUnitPixel = 0;
    private const uint kCGMouseButtonLeft = 0;
    private const uint kCGMouseButtonRight = 1;

    [StructLayout(LayoutKind.Sequential)]
    private struct CGPoint
    {
        public double X, Y;
        
        public CGPoint(double x, double y)
        {
            X = x; Y = y;
        }
    }

    #endregion
}
