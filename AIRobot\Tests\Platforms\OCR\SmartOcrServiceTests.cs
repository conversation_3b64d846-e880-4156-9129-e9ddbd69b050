using System.Drawing;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Platforms.Common.OCR;

namespace AIRobot.Tests.Platforms.OCR;

public class SmartOcrServiceTests
{
    private readonly Mock<IOcrEngine> _mockTesseractEngine;
    private readonly Mock<IOcrEngine> _mockPaddleEngine;
    private readonly Mock<IComputerVisionService> _mockVisionService;
    private readonly Mock<ILogger<SmartOcrService>> _mockLogger;
    private readonly SmartOcrService _ocrService;

    public SmartOcrServiceTests()
    {
        _mockTesseractEngine = new Mock<IOcrEngine>();
        _mockPaddleEngine = new Mock<IOcrEngine>();
        _mockVisionService = new Mock<IComputerVisionService>();
        _mockLogger = new Mock<ILogger<SmartOcrService>>();

        _mockTesseractEngine.Setup(x => x.EngineType).Returns(OcrEngine.Tesseract);
        _mockPaddleEngine.Setup(x => x.EngineType).Returns(OcrEngine.PaddleOCR);

        var engines = new List<IOcrEngine> { _mockTesseractEngine.Object, _mockPaddleEngine.Object };
        
        _ocrService = new SmartOcrService(engines, _mockVisionService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task RecognizeTextAsync_WithAutoEngine_ShouldSelectBestEngine()
    {
        // Arrange
        var imageData = new byte[] { 1, 2, 3, 4 };
        var expectedResult = new OcrResult
        {
            Text = "测试文本",
            Confidence = 0.95f,
            UsedEngine = OcrEngine.Tesseract
        };

        _mockVisionService.Setup(x => x.DetectContoursAsync(It.IsAny<byte[]>()))
            .ReturnsAsync(new List<ContourInfo>());

        _mockTesseractEngine.Setup(x => x.RecognizeTextAsync(It.IsAny<byte[]>(), It.IsAny<string>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _ocrService.RecognizeTextAsync(imageData, OcrEngine.Auto);

        // Assert
        result.Should().NotBeNull();
        result.Text.Should().Be("测试文本");
        result.Confidence.Should().Be(0.95f);
        
        _mockTesseractEngine.Verify(x => x.RecognizeTextAsync(imageData, It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task RecognizeTextAsync_WithSpecificEngine_ShouldUseSpecifiedEngine()
    {
        // Arrange
        var imageData = new byte[] { 1, 2, 3, 4 };
        var expectedResult = new OcrResult
        {
            Text = "PaddleOCR 结果",
            Confidence = 0.98f,
            UsedEngine = OcrEngine.PaddleOCR
        };

        _mockPaddleEngine.Setup(x => x.RecognizeTextAsync(It.IsAny<byte[]>(), It.IsAny<string>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _ocrService.RecognizeTextAsync(imageData, OcrEngine.PaddleOCR);

        // Assert
        result.Should().NotBeNull();
        result.Text.Should().Be("PaddleOCR 结果");
        result.UsedEngine.Should().Be(OcrEngine.PaddleOCR);
        
        _mockPaddleEngine.Verify(x => x.RecognizeTextAsync(imageData, It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task FindTextAsync_WithMatchingText_ShouldReturnMatchingRegions()
    {
        // Arrange
        var imageData = new byte[] { 1, 2, 3, 4 };
        var searchText = "测试";
        
        var ocrResult = new OcrResult
        {
            Text = "这是一个测试文本",
            Regions = new List<TextRegion>
            {
                new TextRegion { Text = "这是", Bounds = new Rectangle(0, 0, 50, 20) },
                new TextRegion { Text = "一个", Bounds = new Rectangle(50, 0, 50, 20) },
                new TextRegion { Text = "测试", Bounds = new Rectangle(100, 0, 50, 20) },
                new TextRegion { Text = "文本", Bounds = new Rectangle(150, 0, 50, 20) }
            }
        };

        _mockVisionService.Setup(x => x.DetectContoursAsync(It.IsAny<byte[]>()))
            .ReturnsAsync(new List<ContourInfo>());

        _mockTesseractEngine.Setup(x => x.RecognizeTextAsync(It.IsAny<byte[]>(), It.IsAny<string>()))
            .ReturnsAsync(ocrResult);

        // Act
        var result = await _ocrService.FindTextAsync(imageData, searchText);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result.First().Text.Should().Be("测试");
        result.First().Bounds.Should().Be(new Rectangle(100, 0, 50, 20));
    }

    [Fact]
    public async Task ContainsTextAsync_WithExistingText_ShouldReturnTrue()
    {
        // Arrange
        var imageData = new byte[] { 1, 2, 3, 4 };
        var searchText = "测试";
        
        var ocrResult = new OcrResult
        {
            Text = "这是一个测试文本",
            Regions = new List<TextRegion>
            {
                new TextRegion { Text = "测试", Bounds = new Rectangle(100, 0, 50, 20) }
            }
        };

        _mockVisionService.Setup(x => x.DetectContoursAsync(It.IsAny<byte[]>()))
            .ReturnsAsync(new List<ContourInfo>());

        _mockTesseractEngine.Setup(x => x.RecognizeTextAsync(It.IsAny<byte[]>(), It.IsAny<string>()))
            .ReturnsAsync(ocrResult);

        // Act
        var result = await _ocrService.ContainsTextAsync(imageData, searchText);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ContainsTextAsync_WithNonExistingText_ShouldReturnFalse()
    {
        // Arrange
        var imageData = new byte[] { 1, 2, 3, 4 };
        var searchText = "不存在";
        
        var ocrResult = new OcrResult
        {
            Text = "这是一个测试文本",
            Regions = new List<TextRegion>
            {
                new TextRegion { Text = "测试", Bounds = new Rectangle(100, 0, 50, 20) }
            }
        };

        _mockVisionService.Setup(x => x.DetectContoursAsync(It.IsAny<byte[]>()))
            .ReturnsAsync(new List<ContourInfo>());

        _mockTesseractEngine.Setup(x => x.RecognizeTextAsync(It.IsAny<byte[]>(), It.IsAny<string>()))
            .ReturnsAsync(ocrResult);

        // Act
        var result = await _ocrService.ContainsTextAsync(imageData, searchText);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task RecognizeFormulaAsync_ShouldReturnFormulaResult()
    {
        // Arrange
        var imageData = new byte[] { 1, 2, 3, 4 };
        var ocrResult = new OcrResult
        {
            Text = "x^2 + y^2 = z^2",
            Confidence = 0.9f,
            Regions = new List<TextRegion>
            {
                new TextRegion { Text = "x^2 + y^2 = z^2", Bounds = new Rectangle(0, 0, 200, 30) }
            }
        };

        _mockVisionService.Setup(x => x.DetectContoursAsync(It.IsAny<byte[]>()))
            .ReturnsAsync(new List<ContourInfo>());

        _mockTesseractEngine.Setup(x => x.RecognizeTextAsync(It.IsAny<byte[]>(), It.IsAny<string>()))
            .ReturnsAsync(ocrResult);

        // Act
        var result = await _ocrService.RecognizeFormulaAsync(imageData);

        // Assert
        result.Should().NotBeNull();
        result.LaTeX.Should().Be("x^2 + y^2 = z^2");
        result.Confidence.Should().BeGreaterThan(0);
        result.Type.Should().Be(FormulaType.Inline);
    }

    [Fact]
    public async Task GetAvailableEnginesAsync_ShouldReturnAllEngines()
    {
        // Act
        var engines = await _ocrService.GetAvailableEnginesAsync();

        // Assert
        engines.Should().NotBeNull();
        engines.Should().Contain(OcrEngine.Tesseract);
        engines.Should().Contain(OcrEngine.PaddleOCR);
        engines.Should().HaveCount(2);
    }
}
