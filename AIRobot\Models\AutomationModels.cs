using System.Diagnostics;

namespace AIRobot.Core.Models;

/// <summary>
/// 自动化任务
/// </summary>
public class AutomationTask
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public IList<AutomationStep> Steps { get; set; } = new List<AutomationStep>();
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 自动化步骤基类
/// </summary>
public abstract class AutomationStep
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Order { get; set; }
    public bool IsEnabled { get; set; } = true;
    public TimeSpan Delay { get; set; } = TimeSpan.Zero;
    
    public abstract Task<StepResult> ExecuteAsync(IAutomationContext context);
}

/// <summary>
/// 步骤执行结果
/// </summary>
public class StepResult
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public Exception? Exception { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}

/// <summary>
/// 自动化上下文接口
/// </summary>
public interface IAutomationContext
{
    T GetService<T>() where T : class;
    void SetVariable(string name, object value);
    T? GetVariable<T>(string name);
    bool HasVariable(string name);
    void Log(Microsoft.Extensions.Logging.LogLevel level, string message);
    Task DelayAsync(TimeSpan delay);
}

/// <summary>
/// 任务执行结果
/// </summary>
public class TaskExecutionResult
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public IList<StepResult> StepResults { get; set; } = new List<StepResult>();
    public TimeSpan TotalExecutionTime { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
}

/// <summary>
/// 任务执行状态
/// </summary>
public enum TaskExecutionStatus
{
    NotStarted,
    Running,
    Paused,
    Completed,
    Failed,
    Cancelled
}
