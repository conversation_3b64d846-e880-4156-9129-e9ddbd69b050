using AIRobot.Core.Interfaces;
using AIRobot.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AIRobot.Platforms.macOS;

/// <summary>
/// macOS平台服务工厂实现
/// </summary>
public class MacOsPlatformServiceFactory : IPlatformServiceFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MacOsPlatformServiceFactory> _logger;

    public MacOsPlatformServiceFactory(IServiceProvider serviceProvider, ILogger<MacOsPlatformServiceFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public string GetPlatformName() => "macOS";

    public bool IsPlatformSupported()
    {
        try
        {
            return OperatingSystem.IsMacOS();
        }
        catch
        {
            return false;
        }
    }

    public IScreenCapture CreateScreenCapture()
    {
        try
        {
            _logger.LogInformation("创建macOS屏幕捕获服务");
            
            if (!IsPlatformSupported())
            {
                throw new PlatformNotSupportedException("当前平台不支持macOS屏幕捕获");
            }

            return _serviceProvider.GetRequiredService<MacOsScreenCapture>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建macOS屏幕捕获服务失败");
            throw;
        }
    }

    public IInputSimulator CreateInputSimulator()
    {
        try
        {
            _logger.LogInformation("创建macOS输入模拟服务");
            
            if (!IsPlatformSupported())
            {
                throw new PlatformNotSupportedException("当前平台不支持macOS输入模拟");
            }

            return _serviceProvider.GetRequiredService<MacOsInputSimulator>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建macOS输入模拟服务失败");
            throw;
        }
    }

    public IApplicationController CreateApplicationController()
    {
        try
        {
            _logger.LogInformation("创建macOS应用程序控制服务");
            
            if (!IsPlatformSupported())
            {
                throw new PlatformNotSupportedException("当前平台不支持macOS应用程序控制");
            }

            return _serviceProvider.GetRequiredService<MacOsApplicationController>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建macOS应用程序控制服务失败");
            throw;
        }
    }

    public IOcrService CreateOcrService()
    {
        try
        {
            _logger.LogInformation("创建OCR服务 (跨平台)");
            
            // OCR服务是跨平台的，直接返回智能OCR服务
            return _serviceProvider.GetRequiredService<SmartOcrService>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建OCR服务失败");
            throw;
        }
    }

    public IComputerVisionService CreateComputerVisionService()
    {
        try
        {
            _logger.LogInformation("创建计算机视觉服务 (跨平台)");
            
            // 计算机视觉服务是跨平台的，直接返回OpenCV服务
            return _serviceProvider.GetRequiredService<OpenCvComputerVisionService>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建计算机视觉服务失败");
            throw;
        }
    }

    public IAutomationEngine CreateAutomationEngine()
    {
        try
        {
            _logger.LogInformation("创建自动化引擎 (跨平台)");
            
            // 自动化引擎是跨平台的
            return _serviceProvider.GetRequiredService<AutomationEngine>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建自动化引擎失败");
            throw;
        }
    }

    public bool CheckPlatformRequirements()
    {
        try
        {
            _logger.LogInformation("检查macOS平台要求");

            if (!IsPlatformSupported())
            {
                _logger.LogWarning("当前不是macOS平台");
                return false;
            }

            // 检查macOS版本
            var version = Environment.OSVersion.Version;
            if (version.Major < 10 || (version.Major == 10 && version.Minor < 15))
            {
                _logger.LogWarning($"macOS版本过低: {version}，建议使用macOS 10.15或更高版本");
                return false;
            }

            // 检查权限
            if (!CheckAccessibilityPermissions())
            {
                _logger.LogWarning("缺少辅助功能权限，某些功能可能无法正常工作");
                return false;
            }

            if (!CheckScreenRecordingPermissions())
            {
                _logger.LogWarning("缺少屏幕录制权限，屏幕捕获功能可能无法正常工作");
                return false;
            }

            _logger.LogInformation("macOS平台要求检查通过");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查macOS平台要求失败");
            return false;
        }
    }

    private bool CheckAccessibilityPermissions()
    {
        try
        {
            // 检查辅助功能权限
            // 在实际实现中，这里应该调用macOS API检查权限
            return true; // 暂时返回true
        }
        catch
        {
            return false;
        }
    }

    private bool CheckScreenRecordingPermissions()
    {
        try
        {
            // 检查屏幕录制权限
            // 在实际实现中，这里应该调用macOS API检查权限
            return true; // 暂时返回true
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// macOS平台服务注册扩展
/// </summary>
public static class MacOsServiceCollectionExtensions
{
    /// <summary>
    /// 注册macOS平台服务
    /// </summary>
    public static IServiceCollection AddMacOsPlatformServices(this IServiceCollection services)
    {
        // 注册macOS特定服务
        services.AddTransient<MacOsScreenCapture>();
        services.AddTransient<MacOsInputSimulator>();
        services.AddTransient<MacOsApplicationController>();
        services.AddTransient<MacOsPlatformServiceFactory>();

        return services;
    }
}
