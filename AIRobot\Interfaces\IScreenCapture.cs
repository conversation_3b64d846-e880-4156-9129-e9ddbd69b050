using System.Drawing;
using AIRobot.Core.Models;

namespace AIRobot.Core.Interfaces;

/// <summary>
/// 屏幕捕获接口
/// </summary>
public interface IScreenCapture
{
    /// <summary>
    /// 捕获整个屏幕
    /// </summary>
    Task<byte[]> CaptureScreenAsync();
    
    /// <summary>
    /// 捕获指定窗口
    /// </summary>
    Task<byte[]> CaptureWindowAsync(IntPtr windowHandle);
    
    /// <summary>
    /// 捕获屏幕区域
    /// </summary>
    Task<byte[]> CaptureRegionAsync(Rectangle region);
    
    /// <summary>
    /// 获取窗口边界
    /// </summary>
    Task<Rectangle> GetWindowBoundsAsync(IntPtr windowHandle);
    
    /// <summary>
    /// 获取所有可见窗口
    /// </summary>
    Task<IEnumerable<WindowInfo>> GetVisibleWindowsAsync();
}
