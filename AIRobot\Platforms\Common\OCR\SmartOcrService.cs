using System.Drawing;
using Microsoft.Extensions.Logging;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Core.Exceptions;

namespace AIRobot.Platforms.Common.OCR;

/// <summary>
/// 智能 OCR 服务实现
/// </summary>
public class SmartOcrService : IOcrService
{
    private readonly Dictionary<OcrEngine, IOcrEngine> _engines;
    private readonly ILogger<SmartOcrService> _logger;
    private readonly IComputerVisionService _visionService;

    public SmartOcrService(
        IEnumerable<IOcrEngine> engines,
        IComputerVisionService visionService,
        ILogger<SmartOcrService> logger)
    {
        _engines = engines.ToDictionary(e => e.EngineType, e => e);
        _visionService = visionService;
        _logger = logger;
        
        _logger.LogInformation("智能 OCR 服务初始化，可用引擎: {Engines}", 
            string.Join(", ", _engines.Keys));
    }

    public async Task<OcrResult> RecognizeTextAsync(byte[] imageData, OcrEngine engine = OcrEngine.Auto)
    {
        try
        {
            if (engine == OcrEngine.Auto)
            {
                engine = await SelectBestEngineAsync(imageData);
                _logger.LogDebug("自动选择 OCR 引擎: {Engine}", engine);
            }

            if (!_engines.TryGetValue(engine, out var ocrEngine))
            {
                _logger.LogWarning("OCR 引擎 {Engine} 不可用，回退到 Tesseract", engine);
                engine = OcrEngine.Tesseract;
                ocrEngine = _engines[engine];
            }

            _logger.LogDebug("使用 OCR 引擎: {Engine}", engine);
            var result = await ocrEngine.RecognizeTextAsync(imageData);
            
            _logger.LogInformation("OCR 识别完成，引擎: {Engine}, 文本长度: {Length}, 置信度: {Confidence}", 
                engine, result.Text.Length, result.Confidence);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OCR 识别失败");
            throw new OcrException("OCR 识别失败", ex);
        }
    }

    public async Task<IEnumerable<TextRegion>> FindTextAsync(byte[] imageData, string searchText)
    {
        try
        {
            _logger.LogDebug("查找文本: '{SearchText}'", searchText);
            
            var result = await RecognizeTextAsync(imageData);
            var foundRegions = result.Regions.Where(region => 
                region.Text.Contains(searchText, StringComparison.OrdinalIgnoreCase)).ToList();
            
            _logger.LogDebug("找到 {Count} 个匹配的文本区域", foundRegions.Count);
            return foundRegions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "查找文本失败: '{SearchText}'", searchText);
            throw new OcrException("查找文本失败", ex);
        }
    }

    public async Task<bool> ContainsTextAsync(byte[] imageData, string searchText)
    {
        try
        {
            var regions = await FindTextAsync(imageData, searchText);
            var contains = regions.Any();
            
            _logger.LogDebug("文本包含检查: '{SearchText}' = {Contains}", searchText, contains);
            return contains;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文本包含检查失败: '{SearchText}'", searchText);
            return false;
        }
    }

    public async Task<FormulaResult> RecognizeFormulaAsync(byte[] imageData)
    {
        try
        {
            _logger.LogDebug("开始公式识别");
            
            // 优先使用 Surya 引擎进行公式识别
            if (_engines.ContainsKey(OcrEngine.Surya))
            {
                // 这里应该调用 Surya 的公式识别功能
                // 暂时使用简化实现
                var result = await RecognizeTextAsync(imageData, OcrEngine.Surya);
                
                return new FormulaResult
                {
                    LaTeX = result.Text,
                    Confidence = result.Confidence,
                    Bounds = result.Regions.FirstOrDefault()?.Bounds ?? Rectangle.Empty,
                    Type = DetermineFormulaType(result.Text)
                };
            }
            else
            {
                _logger.LogWarning("Surya 引擎不可用，使用 Tesseract 进行公式识别");
                var result = await RecognizeTextAsync(imageData, OcrEngine.Tesseract);
                
                return new FormulaResult
                {
                    LaTeX = result.Text,
                    Confidence = result.Confidence * 0.7f, // 降低置信度
                    Bounds = result.Regions.FirstOrDefault()?.Bounds ?? Rectangle.Empty,
                    Type = FormulaType.Inline
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "公式识别失败");
            throw new OcrException("公式识别失败", ex);
        }
    }

    public async Task<TableResult> RecognizeTableAsync(byte[] imageData)
    {
        try
        {
            _logger.LogDebug("开始表格识别");
            
            var result = await RecognizeTextAsync(imageData);
            
            // 简化的表格识别实现
            var lines = result.Regions.GroupBy(r => r.LineNumber).OrderBy(g => g.Key).ToList();
            var cells = new List<TableCell>();
            
            for (int row = 0; row < lines.Count; row++)
            {
                var lineRegions = lines[row].OrderBy(r => r.Bounds.X).ToList();
                
                for (int col = 0; col < lineRegions.Count; col++)
                {
                    var region = lineRegions[col];
                    cells.Add(new TableCell
                    {
                        Text = region.Text,
                        Row = row,
                        Column = col,
                        Bounds = region.Bounds
                    });
                }
            }
            
            var tableResult = new TableResult
            {
                Rows = lines.Count,
                Columns = lines.Count > 0 ? lines.Max(l => l.Count()) : 0,
                Cells = cells,
                Bounds = GetBoundingRectangle(result.Regions),
                Confidence = result.Confidence
            };
            
            _logger.LogDebug("表格识别完成，行数: {Rows}, 列数: {Columns}", 
                tableResult.Rows, tableResult.Columns);
            
            return tableResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "表格识别失败");
            throw new OcrException("表格识别失败", ex);
        }
    }

    public async Task SetLanguageAsync(string languageCode)
    {
        try
        {
            _logger.LogInformation("设置 OCR 语言: {Language}", languageCode);
            
            var tasks = _engines.Values.Select(engine => engine.SetLanguageAsync(languageCode));
            await Task.WhenAll(tasks);
            
            _logger.LogDebug("OCR 语言设置完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置 OCR 语言失败: {Language}", languageCode);
            throw new OcrException("设置 OCR 语言失败", ex);
        }
    }

    public async Task<IEnumerable<OcrEngine>> GetAvailableEnginesAsync()
    {
        return await Task.FromResult(_engines.Keys);
    }

    private async Task<OcrEngine> SelectBestEngineAsync(byte[] imageData)
    {
        try
        {
            // 分析图像特征来选择最佳引擎
            var imageAnalysis = await AnalyzeImageAsync(imageData);

            // 如果检测到数学公式，优先使用 Surya
            if (imageAnalysis.ContainsMathFormula && _engines.ContainsKey(OcrEngine.Surya))
            {
                return OcrEngine.Surya;
            }

            // 如果检测到表格，优先使用 PaddleOCR
            if (imageAnalysis.ContainsTable && _engines.ContainsKey(OcrEngine.PaddleOCR))
            {
                return OcrEngine.PaddleOCR;
            }

            // 如果主要是中英文文本，使用 PaddleOCR
            if ((imageAnalysis.PrimaryLanguage == "zh" || imageAnalysis.PrimaryLanguage == "en") 
                && _engines.ContainsKey(OcrEngine.PaddleOCR))
            {
                return OcrEngine.PaddleOCR;
            }

            // 默认使用 Tesseract
            return OcrEngine.Tesseract;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "图像分析失败，使用默认引擎 Tesseract");
            return OcrEngine.Tesseract;
        }
    }

    private async Task<ImageAnalysis> AnalyzeImageAsync(byte[] imageData)
    {
        try
        {
            // 使用计算机视觉服务进行图像分析
            var contours = await _visionService.DetectContoursAsync(imageData);
            
            var analysis = new ImageAnalysis();
            
            // 简化的分析逻辑
            analysis.ContainsMathFormula = await DetectMathSymbolsAsync(imageData);
            analysis.ContainsTable = contours.Count() > 10; // 简化的表格检测
            analysis.PrimaryLanguage = "en"; // 简化实现
            analysis.TextDensity = contours.Count() / 1000f;
            
            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "图像分析失败");
            return new ImageAnalysis { PrimaryLanguage = "en" };
        }
    }

    private async Task<bool> DetectMathSymbolsAsync(byte[] imageData)
    {
        try
        {
            // 简化的数学符号检测
            var result = await _engines[OcrEngine.Tesseract].RecognizeTextAsync(imageData);
            var mathSymbols = new[] { "∫", "∑", "√", "π", "α", "β", "γ", "=", "+", "-", "*", "/", "^", "²", "³" };
            
            return mathSymbols.Any(symbol => result.Text.Contains(symbol));
        }
        catch
        {
            return false;
        }
    }

    private FormulaType DetermineFormulaType(string latex)
    {
        if (latex.Contains("\\begin{matrix}") || latex.Contains("\\begin{pmatrix}"))
            return FormulaType.Matrix;
        if (latex.Contains("\\ce{") || latex.Contains("\\chemfig"))
            return FormulaType.Chemical;
        if (latex.StartsWith("$$") && latex.EndsWith("$$"))
            return FormulaType.Display;
        return FormulaType.Inline;
    }

    private Rectangle GetBoundingRectangle(IEnumerable<TextRegion> regions)
    {
        if (!regions.Any()) return Rectangle.Empty;
        
        var minX = regions.Min(r => r.Bounds.X);
        var minY = regions.Min(r => r.Bounds.Y);
        var maxX = regions.Max(r => r.Bounds.Right);
        var maxY = regions.Max(r => r.Bounds.Bottom);
        
        return new Rectangle(minX, minY, maxX - minX, maxY - minY);
    }
}
