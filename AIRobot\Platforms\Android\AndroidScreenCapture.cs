using System.Drawing;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using Microsoft.Extensions.Logging;

#if ANDROID
using Android.Graphics;
using Android.Media;
using AndroidX.Core.Content;
using Android.Content;
using Android.Hardware.Display;
using Android.Util;
using Java.IO;
using Java.Nio;
using Android.App;
using Android.Content.PM;
using AndroidX.Core.App;
#endif

namespace AIRobot.Platforms.Android;

/// <summary>
/// Android平台屏幕捕获实现
/// 使用MediaProjection API进行屏幕捕获
/// </summary>
public class AndroidScreenCapture : IScreenCapture
{
    private readonly ILogger<AndroidScreenCapture> _logger;

#if ANDROID
    private MediaProjection? _mediaProjection;
    private ImageReader? _imageReader;
    private VirtualDisplay? _virtualDisplay;
    private readonly Context _context;

    public AndroidScreenCapture(ILogger<AndroidScreenCapture> logger, Context context)
    {
        _logger = logger;
        _context = context;
    }
#else
    public AndroidScreenCapture(ILogger<AndroidScreenCapture> logger)
    {
        _logger = logger;
    }
#endif

    public async Task<byte[]> CaptureScreenAsync()
    {
        try
        {
            _logger.LogInformation("开始捕获Android全屏");

#if ANDROID
            if (!await EnsurePermissionsAsync())
            {
                throw new UnauthorizedAccessException("缺少屏幕捕获权限");
            }

            await InitializeMediaProjectionAsync();
            
            var displayMetrics = _context.Resources?.DisplayMetrics;
            if (displayMetrics == null)
            {
                throw new InvalidOperationException("无法获取显示器信息");
            }

            var width = displayMetrics.WidthPixels;
            var height = displayMetrics.HeightPixels;
            var density = displayMetrics.DensityDpi;

            var imageData = await CaptureScreenInternalAsync(width, height, density);
            _logger.LogInformation($"Android全屏捕获成功，大小: {imageData.Length} 字节");
            return imageData;
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Android全屏捕获失败");
            throw;
        }
    }

    public async Task<byte[]> CaptureWindowAsync(IntPtr windowHandle)
    {
        try
        {
            _logger.LogInformation($"开始捕获Android窗口: {windowHandle}");

#if ANDROID
            // Android中没有传统的窗口概念，这里捕获整个屏幕
            // 在实际应用中，可能需要根据应用程序信息来确定捕获区域
            return await CaptureScreenAsync();
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Android窗口捕获失败: {windowHandle}");
            throw;
        }
    }

    public async Task<byte[]> CaptureRegionAsync(System.Drawing.Rectangle region)
    {
        try
        {
            _logger.LogInformation($"开始捕获Android区域: {region}");

#if ANDROID
            if (!await EnsurePermissionsAsync())
            {
                throw new UnauthorizedAccessException("缺少屏幕捕获权限");
            }

            await InitializeMediaProjectionAsync();
            
            var displayMetrics = _context.Resources?.DisplayMetrics;
            if (displayMetrics == null)
            {
                throw new InvalidOperationException("无法获取显示器信息");
            }

            var density = displayMetrics.DensityDpi;
            var fullScreenData = await CaptureScreenInternalAsync(region.Width, region.Height, density);
            
            // 在实际实现中，这里应该裁剪图像到指定区域
            // 暂时返回全屏数据
            _logger.LogInformation($"Android区域捕获成功，大小: {fullScreenData.Length} 字节");
            return fullScreenData;
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Android区域捕获失败: {region}");
            throw;
        }
    }

    public async Task<IEnumerable<WindowInfo>> GetVisibleWindowsAsync()
    {
        try
        {
            _logger.LogInformation("开始获取Android可见窗口列表");

#if ANDROID
            var windows = new List<WindowInfo>();
            
            // Android中获取运行中的应用程序
            var activityManager = _context.GetSystemService(Context.ActivityService) as ActivityManager;
            if (activityManager != null)
            {
                var runningTasks = activityManager.GetRunningTasks(10);
                foreach (var task in runningTasks)
                {
                    if (task.TopActivity != null)
                    {
                        var packageManager = _context.PackageManager;
                        try
                        {
                            var appInfo = packageManager?.GetApplicationInfo(task.TopActivity.PackageName, 0);
                            var appName = packageManager?.GetApplicationLabel(appInfo)?.ToString() ?? task.TopActivity.PackageName;
                            
                            var windowInfo = new WindowInfo
                            {
                                Handle = new IntPtr(task.Id),
                                Title = appName,
                                ProcessName = task.TopActivity.PackageName,
                                Bounds = GetScreenBounds(),
                                IsVisible = true,
                                IsMinimized = false
                            };
                            
                            windows.Add(windowInfo);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"获取应用信息失败: {task.TopActivity.PackageName}");
                        }
                    }
                }
            }

            _logger.LogInformation($"获取到 {windows.Count} 个Android可见窗口");
            return windows;
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取Android可见窗口列表失败");
            return Enumerable.Empty<WindowInfo>();
        }
    }

#if ANDROID
    private async Task<bool> EnsurePermissionsAsync()
    {
        try
        {
            // 检查屏幕捕获权限
            var activity = Platform.CurrentActivity as Activity;
            if (activity == null)
            {
                _logger.LogError("无法获取当前Activity");
                return false;
            }

            // 检查是否已有权限
            var mediaProjectionManager = _context.GetSystemService(Context.MediaProjectionService) as MediaProjectionManager;
            if (mediaProjectionManager == null)
            {
                _logger.LogError("无法获取MediaProjectionManager");
                return false;
            }

            // 在实际应用中，这里需要请求用户授权
            // 暂时假设已有权限
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查权限失败");
            return false;
        }
    }

    private async Task InitializeMediaProjectionAsync()
    {
        if (_mediaProjection != null) return;

        try
        {
            var mediaProjectionManager = _context.GetSystemService(Context.MediaProjectionService) as MediaProjectionManager;
            if (mediaProjectionManager == null)
            {
                throw new InvalidOperationException("无法获取MediaProjectionManager");
            }

            // 在实际应用中，这里需要从用户授权结果中获取MediaProjection
            // 暂时创建一个空的MediaProjection
            // _mediaProjection = mediaProjectionManager.GetMediaProjection(resultCode, data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化MediaProjection失败");
            throw;
        }
    }

    private async Task<byte[]> CaptureScreenInternalAsync(int width, int height, int density)
    {
        try
        {
            if (_mediaProjection == null)
            {
                throw new InvalidOperationException("MediaProjection未初始化");
            }

            // 创建ImageReader
            _imageReader = ImageReader.NewInstance(width, height, ImageFormatType.Rgba8888, 1);
            
            var tcs = new TaskCompletionSource<byte[]>();
            
            _imageReader.SetOnImageAvailableListener(new ImageAvailableListener(tcs), null);

            // 创建VirtualDisplay
            _virtualDisplay = _mediaProjection.CreateVirtualDisplay(
                "ScreenCapture",
                width, height, density,
                DisplayFlags.None,
                _imageReader.Surface,
                null, null);

            // 等待图像捕获完成
            var imageData = await tcs.Task;
            
            return imageData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "内部屏幕捕获失败");
            throw;
        }
        finally
        {
            CleanupCapture();
        }
    }

    private void CleanupCapture()
    {
        try
        {
            _virtualDisplay?.Release();
            _virtualDisplay = null;
            
            _imageReader?.Close();
            _imageReader = null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理捕获资源失败");
        }
    }

    private System.Drawing.Rectangle GetScreenBounds()
    {
        var displayMetrics = _context.Resources?.DisplayMetrics;
        if (displayMetrics != null)
        {
            return new System.Drawing.Rectangle(0, 0, displayMetrics.WidthPixels, displayMetrics.HeightPixels);
        }
        return new System.Drawing.Rectangle(0, 0, 1080, 1920); // 默认分辨率
    }

    private class ImageAvailableListener : Java.Lang.Object, ImageReader.IOnImageAvailableListener
    {
        private readonly TaskCompletionSource<byte[]> _tcs;

        public ImageAvailableListener(TaskCompletionSource<byte[]> tcs)
        {
            _tcs = tcs;
        }

        public void OnImageAvailable(ImageReader reader)
        {
            try
            {
                using var image = reader.AcquireLatestImage();
                if (image != null)
                {
                    var planes = image.GetPlanes();
                    if (planes != null && planes.Length > 0)
                    {
                        var buffer = planes[0].Buffer;
                        if (buffer != null)
                        {
                            var imageData = new byte[buffer.Remaining()];
                            buffer.Get(imageData);
                            _tcs.SetResult(imageData);
                            return;
                        }
                    }
                }
                
                _tcs.SetException(new InvalidOperationException("无法获取图像数据"));
            }
            catch (Exception ex)
            {
                _tcs.SetException(ex);
            }
        }
    }
#endif

    public void Dispose()
    {
#if ANDROID
        CleanupCapture();
        _mediaProjection?.Stop();
        _mediaProjection = null;
#endif
    }
}
