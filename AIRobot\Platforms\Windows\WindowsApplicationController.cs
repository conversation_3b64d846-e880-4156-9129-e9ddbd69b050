using System.Diagnostics;
using System.Text;
using Microsoft.Extensions.Logging;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Core.Exceptions;

namespace AIRobot.Platforms.Windows;

/// <summary>
/// Windows 应用程序控制实现
/// </summary>
public class WindowsApplicationController : IApplicationController
{
    private readonly ILogger<WindowsApplicationController> _logger;

    public WindowsApplicationController(ILogger<WindowsApplicationController> logger)
    {
        _logger = logger;
    }

    public async Task<Process> LaunchApplicationAsync(string applicationPath)
    {
        return await Task.Run(() =>
        {
            try
            {
                _logger.LogInformation("启动应用程序: {Path}", applicationPath);

                if (string.IsNullOrEmpty(applicationPath))
                {
                    throw new ArgumentException("应用程序路径不能为空");
                }

                if (!File.Exists(applicationPath))
                {
                    throw new FileNotFoundException($"应用程序文件不存在: {applicationPath}");
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = applicationPath,
                    UseShellExecute = true,
                    WorkingDirectory = Path.GetDirectoryName(applicationPath)
                };

                var process = Process.Start(startInfo);
                
                if (process == null)
                {
                    throw new ApplicationControlException("无法启动应用程序");
                }

                _logger.LogInformation("应用程序启动成功: {ProcessName} (PID: {ProcessId})", 
                    process.ProcessName, process.Id);

                return process;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动应用程序失败: {Path}", applicationPath);
                throw new ApplicationControlException("启动应用程序失败", ex);
            }
        });
    }

    public async Task<bool> IsApplicationRunningAsync(string processName)
    {
        return await Task.Run(() =>
        {
            try
            {
                var processes = Process.GetProcessesByName(processName);
                var isRunning = processes.Length > 0;
                
                _logger.LogDebug("检查应用程序运行状态: {ProcessName} = {IsRunning}", processName, isRunning);
                
                return isRunning;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查应用程序运行状态失败: {ProcessName}", processName);
                return false;
            }
        });
    }

    public async Task CloseApplicationAsync(string processName)
    {
        await Task.Run(() =>
        {
            try
            {
                _logger.LogInformation("关闭应用程序: {ProcessName}", processName);

                var processes = Process.GetProcessesByName(processName);
                
                if (processes.Length == 0)
                {
                    _logger.LogWarning("未找到运行中的应用程序: {ProcessName}", processName);
                    return;
                }

                foreach (var process in processes)
                {
                    try
                    {
                        _logger.LogDebug("关闭进程: {ProcessName} (PID: {ProcessId})", 
                            process.ProcessName, process.Id);

                        // 尝试优雅关闭
                        if (!process.CloseMainWindow())
                        {
                            // 如果优雅关闭失败，强制终止
                            process.Kill();
                            _logger.LogWarning("强制终止进程: {ProcessName} (PID: {ProcessId})", 
                                process.ProcessName, process.Id);
                        }

                        // 等待进程退出
                        if (!process.WaitForExit(5000))
                        {
                            process.Kill();
                            _logger.LogWarning("进程未在指定时间内退出，强制终止: {ProcessName} (PID: {ProcessId})", 
                                process.ProcessName, process.Id);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "关闭进程失败: {ProcessName} (PID: {ProcessId})", 
                            process.ProcessName, process.Id);
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                _logger.LogInformation("应用程序关闭完成: {ProcessName}", processName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "关闭应用程序失败: {ProcessName}", processName);
                throw new ApplicationControlException("关闭应用程序失败", ex);
            }
        });
    }

    public async Task<IntPtr> FindWindowAsync(string windowTitle)
    {
        return await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("查找窗口: {WindowTitle}", windowTitle);

                var handle = Win32Api.FindWindow(null, windowTitle);
                
                if (handle == IntPtr.Zero)
                {
                    _logger.LogDebug("未找到窗口: {WindowTitle}", windowTitle);
                }
                else
                {
                    _logger.LogDebug("找到窗口: {WindowTitle}, Handle: {Handle}", windowTitle, handle);
                }

                return handle;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查找窗口失败: {WindowTitle}", windowTitle);
                throw new ApplicationControlException("查找窗口失败", ex);
            }
        });
    }

    public async Task<IEnumerable<WindowInfo>> GetVisibleWindowsAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                var windows = new List<WindowInfo>();
                
                Win32Api.EnumWindows((hWnd, lParam) =>
                {
                    try
                    {
                        if (Win32Api.IsWindowVisible(hWnd))
                        {
                            var title = GetWindowTitle(hWnd);
                            if (!string.IsNullOrEmpty(title))
                            {
                                Win32Api.GetWindowRect(hWnd, out var rect);
                                Win32Api.GetWindowThreadProcessId(hWnd, out var processId);
                                
                                var processName = "";
                                try
                                {
                                    var process = Process.GetProcessById((int)processId);
                                    processName = process.ProcessName;
                                }
                                catch
                                {
                                    // 忽略无法获取进程名的情况
                                }
                                
                                windows.Add(new WindowInfo
                                {
                                    Handle = hWnd,
                                    Title = title,
                                    ProcessName = processName,
                                    Bounds = new System.Drawing.Rectangle(rect.Left, rect.Top, 
                                        rect.Right - rect.Left, rect.Bottom - rect.Top),
                                    IsVisible = true,
                                    IsMinimized = Win32Api.IsIconic(hWnd),
                                    ProcessId = (int)processId
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "枚举窗口时出错: Handle={Handle}", hWnd);
                    }
                    
                    return true; // 继续枚举
                }, IntPtr.Zero);
                
                _logger.LogDebug("找到 {Count} 个可见窗口", windows.Count);
                return windows;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可见窗口列表失败");
                throw new ApplicationControlException("获取可见窗口列表失败", ex);
            }
        });
    }

    private string GetWindowTitle(IntPtr hWnd)
    {
        try
        {
            var length = Win32Api.GetWindowTextLength(hWnd);
            if (length == 0) return string.Empty;
            
            var title = new StringBuilder(length + 1);
            Win32Api.GetWindowText(hWnd, title, title.Capacity);
            return title.ToString();
        }
        catch
        {
            return string.Empty;
        }
    }
}
