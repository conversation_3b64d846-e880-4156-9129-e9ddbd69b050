<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleName</key>
    <string>AIRobot</string>
    
    <key>CFBundleDisplayName</key>
    <string>AIRobot 自动化机器人</string>
    
    <key>CFBundleIdentifier</key>
    <string>com.airobot.macos</string>
    
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    
    <key>CFBundleSignature</key>
    <string>????</string>
    
    <key>CFBundleExecutable</key>
    <string>AIRobot</string>
    
    <key>CFBundleIconFile</key>
    <string>AppIcon</string>
    
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.productivity</string>
    
    <!-- 权限请求 -->
    <key>NSAppleEventsUsageDescription</key>
    <string>AIRobot 需要 AppleScript 权限来控制其他应用程序和执行自动化任务。</string>
    
    <key>NSSystemAdministrationUsageDescription</key>
    <string>AIRobot 需要系统管理权限来执行高级自动化操作。</string>
    
    <key>NSDesktopFolderUsageDescription</key>
    <string>AIRobot 需要访问桌面文件夹来保存截图和处理文件。</string>
    
    <key>NSDocumentsFolderUsageDescription</key>
    <string>AIRobot 需要访问文档文件夹来保存和读取配置文件。</string>
    
    <key>NSDownloadsFolderUsageDescription</key>
    <string>AIRobot 需要访问下载文件夹来处理下载的文件。</string>
    
    <!-- 屏幕录制权限 -->
    <key>NSScreenCaptureDescription</key>
    <string>AIRobot 需要屏幕录制权限来捕获屏幕截图，这是 OCR 识别和自动化操作的核心功能。</string>
    
    <!-- 辅助功能权限 -->
    <key>NSAccessibilityUsageDescription</key>
    <string>AIRobot 需要辅助功能权限来模拟鼠标和键盘操作，实现自动化控制。</string>
    
    <!-- 相机权限（如果需要） -->
    <key>NSCameraUsageDescription</key>
    <string>AIRobot 可能需要相机权限来捕获图像进行 OCR 识别。</string>
    
    <!-- 网络权限 -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
        <key>NSExceptionDomains</key>
        <dict>
            <key>localhost</key>
            <dict>
                <key>NSExceptionAllowsInsecureHTTPLoads</key>
                <true/>
            </dict>
        </dict>
    </dict>
    
    <!-- 支持的文档类型 -->
    <key>CFBundleDocumentTypes</key>
    <array>
        <dict>
            <key>CFBundleTypeName</key>
            <string>AIRobot Automation Task</string>
            <key>CFBundleTypeRole</key>
            <string>Editor</string>
            <key>LSItemContentTypes</key>
            <array>
                <string>com.airobot.task</string>
            </array>
            <key>LSHandlerRank</key>
            <string>Owner</string>
        </dict>
        <dict>
            <key>CFBundleTypeName</key>
            <string>Image</string>
            <key>CFBundleTypeRole</key>
            <string>Viewer</string>
            <key>LSItemContentTypes</key>
            <array>
                <string>public.image</string>
                <string>public.png</string>
                <string>public.jpeg</string>
            </array>
            <key>LSHandlerRank</key>
            <string>Alternate</string>
        </dict>
    </array>
    
    <!-- URL Schemes -->
    <key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleURLName</key>
            <string>AIRobot URL</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>airobot</string>
            </array>
        </dict>
    </array>
    
    <!-- 高分辨率支持 -->
    <key>NSHighResolutionCapable</key>
    <true/>
    
    <!-- 支持暗色模式 -->
    <key>NSRequiresAquaSystemAppearance</key>
    <false/>
    
    <!-- 主线程检查 -->
    <key>NSMainNibFile</key>
    <string>MainMenu</string>
    
    <!-- 沙盒配置（如果使用 App Store） -->
    <key>com.apple.security.app-sandbox</key>
    <false/>
    
    <!-- 如果启用沙盒，需要以下权限 -->
    <!--
    <key>com.apple.security.device.camera</key>
    <true/>
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    <key>com.apple.security.files.downloads.read-write</key>
    <true/>
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.automation.apple-events</key>
    <true/>
    -->
    
</dict>
</plist>
