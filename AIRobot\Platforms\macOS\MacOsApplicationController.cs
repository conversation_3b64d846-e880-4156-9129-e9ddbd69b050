using System.Diagnostics;
using System.Runtime.InteropServices;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using Microsoft.Extensions.Logging;

namespace AIRobot.Platforms.macOS;

/// <summary>
/// macOS平台应用程序控制器实现
/// 使用NSWorkspace和AppleScript进行应用程序管理
/// </summary>
public class MacOsApplicationController : IApplicationController
{
    private readonly ILogger<MacOsApplicationController> _logger;

    public MacOsApplicationController(ILogger<MacOsApplicationController> logger)
    {
        _logger = logger;
    }

    public async Task<Process> StartApplicationAsync(string applicationPath, string? arguments = null)
    {
        try
        {
            _logger.LogInformation($"启动macOS应用程序: {applicationPath}");

            Process process;

            if (applicationPath.EndsWith(".app", StringComparison.OrdinalIgnoreCase))
            {
                // 使用open命令启动.app应用程序
                var startInfo = new ProcessStartInfo
                {
                    FileName = "open",
                    Arguments = $"-a \"{applicationPath}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                if (!string.IsNullOrEmpty(arguments))
                {
                    startInfo.Arguments += $" --args {arguments}";
                }

                process = Process.Start(startInfo) ?? throw new InvalidOperationException("无法启动应用程序");
            }
            else
            {
                // 直接启动可执行文件
                var startInfo = new ProcessStartInfo
                {
                    FileName = applicationPath,
                    Arguments = arguments ?? "",
                    UseShellExecute = false,
                    CreateNoWindow = false
                };

                process = Process.Start(startInfo) ?? throw new InvalidOperationException("无法启动应用程序");
            }

            // 等待应用程序启动
            await Task.Delay(2000);

            _logger.LogInformation($"macOS应用程序启动成功: {applicationPath}, PID: {process.Id}");
            return process;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"启动macOS应用程序失败: {applicationPath}");
            throw;
        }
    }

    public async Task<bool> IsApplicationRunningAsync(string applicationName)
    {
        try
        {
            _logger.LogInformation($"检查macOS应用程序运行状态: {applicationName}");

            // 使用AppleScript检查应用程序是否运行
            var script = $@"
                tell application ""System Events""
                    return (name of processes) contains ""{applicationName}""
                end tell";

            var result = await ExecuteAppleScriptAsync(script);
            var isRunning = result.Trim().Equals("true", StringComparison.OrdinalIgnoreCase);

            _logger.LogInformation($"macOS应用程序 {applicationName} 运行状态: {isRunning}");
            return isRunning;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"检查macOS应用程序运行状态失败: {applicationName}");
            return false;
        }
    }

    public async Task<WindowInfo?> FindWindowAsync(string windowTitle)
    {
        try
        {
            _logger.LogInformation($"查找macOS窗口: {windowTitle}");

            // 使用AppleScript查找窗口
            var script = $@"
                tell application ""System Events""
                    repeat with proc in processes
                        try
                            repeat with win in windows of proc
                                if name of win contains ""{windowTitle}"" then
                                    return {{name of win, name of proc, position of win, size of win}}
                                end if
                            end repeat
                        end try
                    end repeat
                    return """"
                end tell";

            var result = await ExecuteAppleScriptAsync(script);
            
            if (string.IsNullOrEmpty(result))
            {
                _logger.LogInformation($"未找到macOS窗口: {windowTitle}");
                return null;
            }

            var windowInfo = ParseWindowInfoFromAppleScript(result);
            _logger.LogInformation($"找到macOS窗口: {windowTitle}");
            return windowInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"查找macOS窗口失败: {windowTitle}");
            return null;
        }
    }

    public async Task<bool> ActivateWindowAsync(IntPtr windowHandle)
    {
        try
        {
            _logger.LogInformation($"激活macOS窗口: {windowHandle}");

            // 在macOS中，我们通常通过应用程序名称来激活窗口
            // 这里需要根据窗口句柄获取应用程序信息
            var windowInfo = await GetWindowInfoByHandleAsync(windowHandle);
            if (windowInfo == null)
            {
                return false;
            }

            var script = $@"
                tell application ""{windowInfo.ProcessName}""
                    activate
                end tell";

            await ExecuteAppleScriptAsync(script);
            
            _logger.LogInformation($"macOS窗口激活成功: {windowHandle}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"激活macOS窗口失败: {windowHandle}");
            return false;
        }
    }

    public async Task<bool> CloseWindowAsync(IntPtr windowHandle)
    {
        try
        {
            _logger.LogInformation($"关闭macOS窗口: {windowHandle}");

            var windowInfo = await GetWindowInfoByHandleAsync(windowHandle);
            if (windowInfo == null)
            {
                return false;
            }

            var script = $@"
                tell application ""System Events""
                    tell process ""{windowInfo.ProcessName}""
                        try
                            click button 1 of window ""{windowInfo.Title}""
                        on error
                            keystroke ""w"" using command down
                        end try
                    end tell
                end tell";

            await ExecuteAppleScriptAsync(script);
            
            _logger.LogInformation($"macOS窗口关闭成功: {windowHandle}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"关闭macOS窗口失败: {windowHandle}");
            return false;
        }
    }

    public async Task<bool> MinimizeWindowAsync(IntPtr windowHandle)
    {
        try
        {
            _logger.LogInformation($"最小化macOS窗口: {windowHandle}");

            var windowInfo = await GetWindowInfoByHandleAsync(windowHandle);
            if (windowInfo == null)
            {
                return false;
            }

            var script = $@"
                tell application ""System Events""
                    tell process ""{windowInfo.ProcessName}""
                        set minimized of window ""{windowInfo.Title}"" to true
                    end tell
                end tell";

            await ExecuteAppleScriptAsync(script);
            
            _logger.LogInformation($"macOS窗口最小化成功: {windowHandle}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"最小化macOS窗口失败: {windowHandle}");
            return false;
        }
    }

    public async Task<bool> MaximizeWindowAsync(IntPtr windowHandle)
    {
        try
        {
            _logger.LogInformation($"最大化macOS窗口: {windowHandle}");

            var windowInfo = await GetWindowInfoByHandleAsync(windowHandle);
            if (windowInfo == null)
            {
                return false;
            }

            var script = $@"
                tell application ""System Events""
                    tell process ""{windowInfo.ProcessName}""
                        try
                            click button 2 of window ""{windowInfo.Title}""
                        on error
                            keystroke ""f"" using {{control down, command down}}
                        end try
                    end tell
                end tell";

            await ExecuteAppleScriptAsync(script);
            
            _logger.LogInformation($"macOS窗口最大化成功: {windowHandle}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"最大化macOS窗口失败: {windowHandle}");
            return false;
        }
    }

    public async Task<IEnumerable<WindowInfo>> GetVisibleWindowsAsync()
    {
        try
        {
            _logger.LogInformation("获取macOS可见窗口列表");

            var script = @"
                tell application ""System Events""
                    set windowList to {}
                    repeat with proc in processes
                        try
                            repeat with win in windows of proc
                                if visible of win then
                                    set windowList to windowList & {{name of win, name of proc, position of win, size of win}}
                                end if
                            end repeat
                        end try
                    end repeat
                    return windowList
                end tell";

            var result = await ExecuteAppleScriptAsync(script);
            var windows = ParseWindowListFromAppleScript(result);
            
            _logger.LogInformation($"获取到 {windows.Count()} 个macOS可见窗口");
            return windows;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取macOS可见窗口列表失败");
            return Enumerable.Empty<WindowInfo>();
        }
    }

    private async Task<string> ExecuteAppleScriptAsync(string script)
    {
        try
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "osascript",
                    Arguments = $"-e \"{script.Replace("\"", "\\\"")}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();
            await process.WaitForExitAsync();

            if (process.ExitCode != 0 && !string.IsNullOrEmpty(error))
            {
                throw new InvalidOperationException($"AppleScript执行失败: {error}");
            }

            return output;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"执行AppleScript失败: {script}");
            throw;
        }
    }

    private WindowInfo? ParseWindowInfoFromAppleScript(string result)
    {
        try
        {
            // 解析AppleScript返回的窗口信息
            // 格式: {窗口标题, 进程名称, {x, y}, {width, height}}
            var parts = result.Split(',');
            if (parts.Length < 4) return null;

            var title = parts[0].Trim().Trim('{', '}');
            var processName = parts[1].Trim();
            
            // 解析位置和大小信息
            var x = int.Parse(parts[2].Trim().Trim('{'));
            var y = int.Parse(parts[3].Trim().Trim('}'));
            var width = int.Parse(parts[4].Trim().Trim('{'));
            var height = int.Parse(parts[5].Trim().Trim('}'));

            return new WindowInfo
            {
                Handle = IntPtr.Zero, // macOS中窗口句柄的概念不同
                Title = title,
                ProcessName = processName,
                Bounds = new System.Drawing.Rectangle(x, y, width, height),
                IsVisible = true,
                IsMinimized = false
            };
        }
        catch
        {
            return null;
        }
    }

    private IEnumerable<WindowInfo> ParseWindowListFromAppleScript(string result)
    {
        var windows = new List<WindowInfo>();
        
        try
        {
            // 解析AppleScript返回的窗口列表
            // 这里需要根据实际返回格式进行解析
            var lines = result.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            
            foreach (var line in lines)
            {
                var windowInfo = ParseWindowInfoFromAppleScript(line);
                if (windowInfo != null)
                {
                    windows.Add(windowInfo);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解析窗口列表失败");
        }

        return windows;
    }

    private async Task<WindowInfo?> GetWindowInfoByHandleAsync(IntPtr windowHandle)
    {
        // 在macOS中，窗口句柄的概念与Windows不同
        // 这里需要实现根据句柄获取窗口信息的逻辑
        // 暂时返回null，实际实现中需要维护窗口句柄到窗口信息的映射
        return null;
    }
}
