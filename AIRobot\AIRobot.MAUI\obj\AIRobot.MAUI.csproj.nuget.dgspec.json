{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.MAUI\\AIRobot.MAUI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj", "projectName": "AIRobot.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.MAUI\\AIRobot.MAUI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.MAUI\\AIRobot.MAUI.csproj", "projectName": "AIRobot.MAUI", "projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.MAUI\\AIRobot.MAUI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.MAUI\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-android", "net9.0-ios", "net9.0-maccatalyst", "net9.0-windows10.0.19041.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-android35.0": {"targetAlias": "net9.0-android", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj"}}}, "net9.0-ios18.4": {"targetAlias": "net9.0-ios", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj"}}}, "net9.0-maccatalyst18.4": {"targetAlias": "net9.0-maccatalyst", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj"}}}, "net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-android35.0": {"targetAlias": "net9.0-android", "dependencies": {"CommunityToolkit.Maui": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Components.WebView.Maui": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.Android": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0-ios18.4": {"targetAlias": "net9.0-ios", "dependencies": {"CommunityToolkit.Maui": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Components.WebView.Maui": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.iOS": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0-maccatalyst18.4": {"targetAlias": "net9.0-maccatalyst", "dependencies": {"CommunityToolkit.Maui": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Components.WebView.Maui": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.6, )", "autoReferenced": true}}, "imports": ["xamarinios10", "net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.MacCatalyst": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}, "net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "dependencies": {"CommunityToolkit.Maui": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.Components.WebView.Maui": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.57, 10.0.19041.57]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}, "runtimes": {"android-arm64": {"#import": []}, "android-x64": {"#import": []}, "iossimulator-x64": {"#import": []}, "maccatalyst-x64": {"#import": []}, "win10-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj", "projectName": "AIRobot.Platforms", "projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Platforms.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\workspace\\ai\\AIRobot\\AIRobot.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.10.0.20240616, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.10.0.20240616, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.5, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.0, )"}, "Tesseract": {"target": "Package", "version": "[5.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}