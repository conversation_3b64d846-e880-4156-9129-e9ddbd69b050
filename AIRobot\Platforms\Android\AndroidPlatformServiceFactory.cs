using AIRobot.Core.Interfaces;
using AIRobot.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

#if ANDROID
using Android.Content;
#endif

namespace AIRobot.Platforms.Android;

/// <summary>
/// Android平台服务工厂实现
/// </summary>
public class AndroidPlatformServiceFactory : IPlatformServiceFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AndroidPlatformServiceFactory> _logger;

    public AndroidPlatformServiceFactory(IServiceProvider serviceProvider, ILogger<AndroidPlatformServiceFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public string GetPlatformName() => "Android";

    public bool IsPlatformSupported()
    {
        try
        {
#if ANDROID
            return true;
#else
            return false;
#endif
        }
        catch
        {
            return false;
        }
    }

    public IScreenCapture CreateScreenCapture()
    {
        try
        {
            _logger.LogInformation("创建Android屏幕捕获服务");
            
            if (!IsPlatformSupported())
            {
                throw new PlatformNotSupportedException("当前平台不支持Android屏幕捕获");
            }

#if ANDRO<PERSON>
            return _serviceProvider.GetRequiredService<AndroidScreenCapture>();
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建Android屏幕捕获服务失败");
            throw;
        }
    }

    public IInputSimulator CreateInputSimulator()
    {
        try
        {
            _logger.LogInformation("创建Android输入模拟服务");
            
            if (!IsPlatformSupported())
            {
                throw new PlatformNotSupportedException("当前平台不支持Android输入模拟");
            }

#if ANDROID
            return _serviceProvider.GetRequiredService<AndroidInputSimulator>();
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建Android输入模拟服务失败");
            throw;
        }
    }

    public IApplicationController CreateApplicationController()
    {
        try
        {
            _logger.LogInformation("创建Android应用程序控制服务");
            
            if (!IsPlatformSupported())
            {
                throw new PlatformNotSupportedException("当前平台不支持Android应用程序控制");
            }

#if ANDROID
            return _serviceProvider.GetRequiredService<AndroidApplicationController>();
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建Android应用程序控制服务失败");
            throw;
        }
    }

    public IOcrService CreateOcrService()
    {
        try
        {
            _logger.LogInformation("创建OCR服务 (跨平台)");
            
            // OCR服务是跨平台的，直接返回智能OCR服务
            return _serviceProvider.GetRequiredService<SmartOcrService>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建OCR服务失败");
            throw;
        }
    }

    public IComputerVisionService CreateComputerVisionService()
    {
        try
        {
            _logger.LogInformation("创建计算机视觉服务 (跨平台)");
            
            // 计算机视觉服务是跨平台的，直接返回OpenCV服务
            return _serviceProvider.GetRequiredService<OpenCvComputerVisionService>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建计算机视觉服务失败");
            throw;
        }
    }

    public IAutomationEngine CreateAutomationEngine()
    {
        try
        {
            _logger.LogInformation("创建自动化引擎 (跨平台)");
            
            // 自动化引擎是跨平台的
            return _serviceProvider.GetRequiredService<AutomationEngine>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建自动化引擎失败");
            throw;
        }
    }

    public bool CheckPlatformRequirements()
    {
        try
        {
            _logger.LogInformation("检查Android平台要求");

            if (!IsPlatformSupported())
            {
                _logger.LogWarning("当前不是Android平台");
                return false;
            }

#if ANDROID
            // 检查Android版本
            if (Android.OS.Build.VERSION.SdkInt < Android.OS.BuildVersionCodes.Lollipop)
            {
                _logger.LogWarning($"Android版本过低: API {(int)Android.OS.Build.VERSION.SdkInt}，建议使用API 21或更高版本");
                return false;
            }

            // 检查权限
            if (!CheckScreenCapturePermissions())
            {
                _logger.LogWarning("缺少屏幕捕获权限，屏幕捕获功能可能无法正常工作");
                return false;
            }

            if (!CheckAccessibilityPermissions())
            {
                _logger.LogWarning("缺少辅助功能权限，输入模拟功能可能无法正常工作");
                return false;
            }

            _logger.LogInformation("Android平台要求检查通过");
            return true;
#else
            return false;
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查Android平台要求失败");
            return false;
        }
    }

#if ANDROID
    private bool CheckScreenCapturePermissions()
    {
        try
        {
            // 检查屏幕捕获权限
            // MediaProjection权限需要用户手动授权，这里只能检查是否已授权
            return true; // 暂时返回true，实际实现中需要检查具体权限状态
        }
        catch
        {
            return false;
        }
    }

    private bool CheckAccessibilityPermissions()
    {
        try
        {
            // 检查辅助功能权限
            // 需要检查AccessibilityService是否已启用
            return true; // 暂时返回true，实际实现中需要检查具体权限状态
        }
        catch
        {
            return false;
        }
    }
#endif
}

/// <summary>
/// Android平台服务注册扩展
/// </summary>
public static class AndroidServiceCollectionExtensions
{
    /// <summary>
    /// 注册Android平台服务
    /// </summary>
    public static IServiceCollection AddAndroidPlatformServices(this IServiceCollection services)
    {
#if ANDROID
        // 注册Android特定服务
        services.AddTransient<AndroidScreenCapture>();
        services.AddTransient<AndroidInputSimulator>();
        services.AddTransient<AndroidApplicationController>();
        services.AddTransient<AndroidPlatformServiceFactory>();
#endif

        return services;
    }
}
