using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using FluentAssertions;
using AIRobot.Platforms.Windows;
using System.Drawing;

namespace AIRobot.Tests.PlatformTests;

/// <summary>
/// Windows 平台特定功能测试
/// </summary>
[Collection("Windows Platform Tests")]
public class WindowsPlatformTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly ILogger<WindowsPlatformTests> _logger;

    public WindowsPlatformTests()
    {
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        services.AddWindowsPlatformServices();
        
        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<WindowsPlatformTests>>();
    }

    [SkippableFact]
    public async Task Test_Windows_Screen_Capture()
    {
        Skip.IfNot(OperatingSystem.IsWindows(), "此测试仅在 Windows 平台运行");

        // 安排
        var screenCapture = _serviceProvider.GetRequiredService<WindowsScreenCapture>();

        // 执行和验证
        var screenshot = await screenCapture.CaptureScreenAsync();
        screenshot.Should().NotBeNull().And.NotBeEmpty();

        var windows = await screenCapture.GetVisibleWindowsAsync();
        windows.Should().NotBeNull();

        _logger.LogInformation("Windows 屏幕捕获测试完成: 截图大小 {Size} 字节，找到 {Count} 个窗口", 
            screenshot.Length, windows.Count());
    }

    [SkippableFact]
    public async Task Test_Windows_Input_Simulation()
    {
        Skip.IfNot(OperatingSystem.IsWindows(), "此测试仅在 Windows 平台运行");

        // 安排
        var inputSimulator = _serviceProvider.GetRequiredService<WindowsInputSimulator>();

        // 执行基本输入测试（不会实际影响系统）
        var testPoint = new Point(Screen.PrimaryScreen?.Bounds.Width / 2 ?? 500, 
                                  Screen.PrimaryScreen?.Bounds.Height / 2 ?? 300);

        // 这些测试需要谨慎执行，避免干扰用户操作
        await Task.Delay(100); // 模拟测试延迟

        // 验证方法存在且可调用（不执行实际操作）
        var clickTask = inputSimulator.ClickAsync(testPoint);
        var typeTask = inputSimulator.TypeTextAsync("Test");
        var keyTask = inputSimulator.SendKeyAsync("Escape");

        // 等待短时间后取消，避免实际执行
        using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(100));
        
        try
        {
            await Task.WhenAny(
                Task.WhenAll(clickTask, typeTask, keyTask),
                Task.Delay(200, cts.Token)
            );
        }
        catch (OperationCanceledException)
        {
            // 预期的取消操作
        }

        _logger.LogInformation("Windows 输入模拟测试完成");
    }

    [SkippableFact]
    public async Task Test_Windows_Application_Controller()
    {
        Skip.IfNot(OperatingSystem.IsWindows(), "此测试仅在 Windows 平台运行");

        // 安排
        var appController = _serviceProvider.GetRequiredService<WindowsApplicationController>();

        // 测试获取可见窗口
        var windows = await appController.GetVisibleWindowsAsync();
        windows.Should().NotBeNull();

        // 测试查找系统窗口（如资源管理器）
        var explorerWindow = await appController.FindWindowAsync("资源管理器");
        // 不强制要求找到，因为可能没有打开

        // 测试应用程序运行状态检查
        var isExplorerRunning = await appController.IsApplicationRunningAsync("explorer");
        isExplorerRunning.Should().BeTrue(); // Windows 资源管理器通常在运行

        _logger.LogInformation("Windows 应用程序控制器测试完成: 找到 {Count} 个窗口", windows.Count());
    }

    [SkippableFact]
    public void Test_Windows_Platform_Factory()
    {
        Skip.IfNot(OperatingSystem.IsWindows(), "此测试仅在 Windows 平台运行");

        // 安排
        var factory = _serviceProvider.GetRequiredService<WindowsPlatformServiceFactory>();

        // 验证
        factory.Should().NotBeNull();
        factory.GetPlatformName().Should().Be("Windows");
        factory.IsPlatformSupported().Should().BeTrue();
        factory.CheckPlatformRequirements().Should().BeTrue();

        // 测试服务创建
        var screenCapture = factory.CreateScreenCapture();
        screenCapture.Should().BeOfType<WindowsScreenCapture>();

        var inputSimulator = factory.CreateInputSimulator();
        inputSimulator.Should().BeOfType<WindowsInputSimulator>();

        var appController = factory.CreateApplicationController();
        appController.Should().BeOfType<WindowsApplicationController>();

        _logger.LogInformation("Windows 平台工厂测试完成");
    }

    [SkippableFact]
    public async Task Test_Windows_Win32_API_Integration()
    {
        Skip.IfNot(OperatingSystem.IsWindows(), "此测试仅在 Windows 平台运行");

        // 测试 Win32 API 调用
        var desktopWindow = Win32Api.GetDesktopWindow();
        desktopWindow.Should().NotBe(IntPtr.Zero);

        var foregroundWindow = Win32Api.GetForegroundWindow();
        foregroundWindow.Should().NotBe(IntPtr.Zero);

        // 测试窗口信息获取
        var windowRect = new Win32Api.RECT();
        var result = Win32Api.GetWindowRect(foregroundWindow, out windowRect);
        result.Should().BeTrue();

        windowRect.Right.Should().BeGreaterThan(windowRect.Left);
        windowRect.Bottom.Should().BeGreaterThan(windowRect.Top);

        _logger.LogInformation("Windows Win32 API 集成测试完成");
    }

    [SkippableFact]
    public async Task Test_Windows_Multi_Monitor_Support()
    {
        Skip.IfNot(OperatingSystem.IsWindows(), "此测试仅在 Windows 平台运行");

        // 安排
        var screenCapture = _serviceProvider.GetRequiredService<WindowsScreenCapture>();

        // 获取所有显示器信息
        var screens = Screen.AllScreens;
        screens.Should().NotBeNull().And.NotBeEmpty();

        // 测试主显示器捕获
        var primaryScreenshot = await screenCapture.CaptureScreenAsync();
        primaryScreenshot.Should().NotBeNull().And.NotBeEmpty();

        // 如果有多个显示器，测试特定区域捕获
        if (screens.Length > 1)
        {
            var secondScreen = screens[1];
            var region = new Rectangle(
                secondScreen.Bounds.X, 
                secondScreen.Bounds.Y, 
                Math.Min(200, secondScreen.Bounds.Width), 
                Math.Min(200, secondScreen.Bounds.Height)
            );

            var regionScreenshot = await screenCapture.CaptureRegionAsync(region);
            regionScreenshot.Should().NotBeNull().And.NotBeEmpty();
        }

        _logger.LogInformation("Windows 多显示器支持测试完成: 检测到 {Count} 个显示器", screens.Length);
    }

    [SkippableFact]
    public async Task Test_Windows_Performance_Optimization()
    {
        Skip.IfNot(OperatingSystem.IsWindows(), "此测试仅在 Windows 平台运行");

        // 安排
        var screenCapture = _serviceProvider.GetRequiredService<WindowsScreenCapture>();
        const int iterations = 10;
        var times = new List<TimeSpan>();

        // 执行多次屏幕捕获测试性能
        for (int i = 0; i < iterations; i++)
        {
            var start = DateTime.Now;
            var screenshot = await screenCapture.CaptureScreenAsync();
            var elapsed = DateTime.Now - start;
            
            times.Add(elapsed);
            screenshot.Should().NotBeNull().And.NotBeEmpty();
        }

        // 验证性能指标
        var averageTime = times.Average(t => t.TotalMilliseconds);
        var maxTime = times.Max(t => t.TotalMilliseconds);
        var minTime = times.Min(t => t.TotalMilliseconds);

        averageTime.Should().BeLessThan(2000); // 平均应在 2 秒内
        maxTime.Should().BeLessThan(5000); // 最大应在 5 秒内

        _logger.LogInformation("Windows 性能优化测试完成: 平均 {Avg}ms，最大 {Max}ms，最小 {Min}ms", 
            averageTime, maxTime, minTime);
    }

    [SkippableFact]
    public async Task Test_Windows_Error_Handling()
    {
        Skip.IfNot(OperatingSystem.IsWindows(), "此测试仅在 Windows 平台运行");

        // 安排
        var screenCapture = _serviceProvider.GetRequiredService<WindowsScreenCapture>();
        var inputSimulator = _serviceProvider.GetRequiredService<WindowsInputSimulator>();

        // 测试无效窗口句柄
        var invalidHandle = new IntPtr(-1);
        var captureAction = async () => await screenCapture.CaptureWindowAsync(invalidHandle);
        await captureAction.Should().ThrowAsync<Exception>();

        // 测试无效坐标
        var invalidPoint = new Point(-1000, -1000);
        var clickAction = async () => await inputSimulator.ClickAsync(invalidPoint);
        // 注意：某些无效操作可能不会抛出异常，而是静默失败

        _logger.LogInformation("Windows 错误处理测试完成");
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}
