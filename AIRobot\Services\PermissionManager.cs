using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace AIRobot.Core.Services;

/// <summary>
/// 跨平台权限管理器
/// </summary>
public class PermissionManager
{
    private readonly ILogger<PermissionManager> _logger;

    public PermissionManager(ILogger<PermissionManager> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 检查所有必要权限
    /// </summary>
    public async Task<PermissionStatus> CheckAllPermissionsAsync()
    {
        try
        {
            _logger.LogInformation("开始检查平台权限");

            var status = new PermissionStatus();

            if (OperatingSystem.IsWindows())
            {
                status = await CheckWindowsPermissionsAsync();
            }
            else if (OperatingSystem.IsMacOS())
            {
                status = await CheckMacOSPermissionsAsync();
            }
            else if (OperatingSystem.IsAndroid())
            {
                status = await CheckAndroidPermissionsAsync();
            }
            else
            {
                _logger.LogWarning("不支持的平台权限检查");
                status.IsSupported = false;
            }

            _logger.LogInformation("权限检查完成，状态: {Status}", status.OverallStatus);
            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "权限检查失败");
            return new PermissionStatus { IsSupported = false, ErrorMessage = ex.Message };
        }
    }

    /// <summary>
    /// 请求缺失的权限
    /// </summary>
    public async Task<bool> RequestMissingPermissionsAsync(PermissionStatus status)
    {
        try
        {
            _logger.LogInformation("开始请求缺失权限");

            if (OperatingSystem.IsWindows())
            {
                return await RequestWindowsPermissionsAsync(status);
            }
            else if (OperatingSystem.IsMacOS())
            {
                return await RequestMacOSPermissionsAsync(status);
            }
            else if (OperatingSystem.IsAndroid())
            {
                return await RequestAndroidPermissionsAsync(status);
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "权限请求失败");
            return false;
        }
    }

    private async Task<PermissionStatus> CheckWindowsPermissionsAsync()
    {
        var status = new PermissionStatus { IsSupported = true };

        try
        {
            // Windows 通常不需要特殊权限，但检查 UAC 和管理员权限
            status.ScreenCapture = true; // Windows API 默认可用
            status.InputSimulation = true; // SendInput API 默认可用
            status.ApplicationControl = true; // Process API 默认可用

            // 检查是否以管理员身份运行（可选）
            status.IsElevated = IsRunningAsAdministrator();
            
            _logger.LogInformation("Windows 权限检查完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Windows 权限检查失败");
            status.ErrorMessage = ex.Message;
        }

        return status;
    }

    private async Task<PermissionStatus> CheckMacOSPermissionsAsync()
    {
        var status = new PermissionStatus { IsSupported = true };

        try
        {
            // 检查辅助功能权限
            status.InputSimulation = await CheckMacOSAccessibilityPermissionAsync();
            
            // 检查屏幕录制权限
            status.ScreenCapture = await CheckMacOSScreenRecordingPermissionAsync();
            
            // 应用程序控制通常可用
            status.ApplicationControl = true;

            _logger.LogInformation("macOS 权限检查完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "macOS 权限检查失败");
            status.ErrorMessage = ex.Message;
        }

        return status;
    }

    private async Task<PermissionStatus> CheckAndroidPermissionsAsync()
    {
        var status = new PermissionStatus { IsSupported = true };

        try
        {
            // Android 权限检查需要通过 Java 互操作实现
            // 这里提供框架，具体实现需要在 Android 项目中完成
            
            status.ScreenCapture = false; // 需要 MediaProjection 权限
            status.InputSimulation = false; // 需要辅助功能权限
            status.ApplicationControl = true; // 基本应用控制可用

            _logger.LogInformation("Android 权限检查完成（需要在 Android 项目中实现具体检查）");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Android 权限检查失败");
            status.ErrorMessage = ex.Message;
        }

        return status;
    }

    private async Task<bool> RequestWindowsPermissionsAsync(PermissionStatus status)
    {
        try
        {
            if (!status.IsElevated)
            {
                _logger.LogInformation("Windows 应用程序未以管理员身份运行，某些功能可能受限");
                
                // 可以选择重新启动为管理员
                var restart = await PromptForAdministratorRestartAsync();
                if (restart)
                {
                    RestartAsAdministrator();
                    return true;
                }
            }

            return true; // Windows 通常不需要额外权限请求
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Windows 权限请求失败");
            return false;
        }
    }

    private async Task<bool> RequestMacOSPermissionsAsync(PermissionStatus status)
    {
        try
        {
            var success = true;

            if (!status.ScreenCapture)
            {
                _logger.LogWarning("缺少 macOS 屏幕录制权限");
                success &= await RequestMacOSScreenRecordingPermissionAsync();
            }

            if (!status.InputSimulation)
            {
                _logger.LogWarning("缺少 macOS 辅助功能权限");
                success &= await RequestMacOSAccessibilityPermissionAsync();
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "macOS 权限请求失败");
            return false;
        }
    }

    private async Task<bool> RequestAndroidPermissionsAsync(PermissionStatus status)
    {
        try
        {
            // Android 权限请求需要在 Android 项目中实现
            _logger.LogInformation("Android 权限请求需要在 Android 项目中实现");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Android 权限请求失败");
            return false;
        }
    }

    private bool IsRunningAsAdministrator()
    {
        try
        {
            if (OperatingSystem.IsWindows())
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> CheckMacOSAccessibilityPermissionAsync()
    {
        try
        {
            var script = @"
                tell application ""System Events""
                    return true
                end tell";
            
            var result = await ExecuteAppleScriptAsync(script);
            return !string.IsNullOrEmpty(result) && !result.Contains("error");
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> CheckMacOSScreenRecordingPermissionAsync()
    {
        try
        {
            // 尝试创建一个小的屏幕截图来测试权限
            var script = @"
                tell application ""System Events""
                    try
                        set screenSize to size of desktop
                        return ""success""
                    on error
                        return ""error""
                    end try
                end tell";
            
            var result = await ExecuteAppleScriptAsync(script);
            return result.Contains("success");
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> RequestMacOSAccessibilityPermissionAsync()
    {
        try
        {
            _logger.LogInformation("请求 macOS 辅助功能权限");
            
            // 打开系统偏好设置的辅助功能页面
            var process = Process.Start("open", "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility");
            
            // 显示用户指导
            _logger.LogInformation("请在系统偏好设置中启用 AIRobot 的辅助功能权限");
            
            return true; // 用户需要手动操作
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "请求 macOS 辅助功能权限失败");
            return false;
        }
    }

    private async Task<bool> RequestMacOSScreenRecordingPermissionAsync()
    {
        try
        {
            _logger.LogInformation("请求 macOS 屏幕录制权限");
            
            // 打开系统偏好设置的屏幕录制页面
            var process = Process.Start("open", "x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture");
            
            // 显示用户指导
            _logger.LogInformation("请在系统偏好设置中启用 AIRobot 的屏幕录制权限");
            
            return true; // 用户需要手动操作
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "请求 macOS 屏幕录制权限失败");
            return false;
        }
    }

    private async Task<bool> PromptForAdministratorRestartAsync()
    {
        // 这里应该显示用户对话框询问是否重新启动为管理员
        // 简化实现，返回 false
        return false;
    }

    private void RestartAsAdministrator()
    {
        try
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = Environment.ProcessPath ?? "AIRobot.exe",
                UseShellExecute = true,
                Verb = "runas" // 请求管理员权限
            };
            
            Process.Start(startInfo);
            Environment.Exit(0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新启动为管理员失败");
        }
    }

    private async Task<string> ExecuteAppleScriptAsync(string script)
    {
        try
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "osascript",
                    Arguments = $"-e \"{script.Replace("\"", "\\\"")}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();
            await process.WaitForExitAsync();

            return process.ExitCode == 0 ? output : error;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行 AppleScript 失败");
            return "";
        }
    }
}

/// <summary>
/// 权限状态
/// </summary>
public class PermissionStatus
{
    public bool IsSupported { get; set; } = true;
    public bool ScreenCapture { get; set; }
    public bool InputSimulation { get; set; }
    public bool ApplicationControl { get; set; }
    public bool IsElevated { get; set; }
    public string? ErrorMessage { get; set; }

    public string OverallStatus => IsSupported && ScreenCapture && InputSimulation && ApplicationControl 
        ? "所有权限已授予" 
        : "部分权限缺失";
}
