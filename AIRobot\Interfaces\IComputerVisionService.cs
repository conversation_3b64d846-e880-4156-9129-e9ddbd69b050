using System.Drawing;
using AIRobot.Core.Models;

namespace AIRobot.Core.Interfaces;

/// <summary>
/// 计算机视觉服务接口
/// </summary>
public interface IComputerVisionService
{
    /// <summary>
    /// 图像预处理
    /// </summary>
    Task<byte[]> PreprocessImageAsync(byte[] imageData, ImageProcessingOptions options);
    
    /// <summary>
    /// 检测轮廓
    /// </summary>
    Task<IEnumerable<ContourInfo>> DetectContoursAsync(byte[] imageData);
    
    /// <summary>
    /// 模板匹配
    /// </summary>
    Task<TemplateMatchResult> MatchTemplateAsync(byte[] image, byte[] template);
    
    /// <summary>
    /// 分割对象 (可选 SAM 功能)
    /// </summary>
    Task<SegmentationResult> SegmentObjectAsync(byte[] imageData, Point clickPoint);
    
    /// <summary>
    /// 自动分割
    /// </summary>
    Task<IEnumerable<ObjectSegment>> AutoSegmentAsync(byte[] imageData);
}
