using Microsoft.Extensions.Logging;
using AIRobot.Core.Interfaces;
using AIRobot.Platforms.Common;
using AIRobot.Platforms.Common.OCR;

namespace AIRobot.Platforms.Windows;

/// <summary>
/// Windows 平台服务工厂
/// </summary>
public class WindowsPlatformServiceFactory : IPlatformServiceFactory
{
    private readonly ILoggerFactory _loggerFactory;

    public WindowsPlatformServiceFactory(ILoggerFactory loggerFactory)
    {
        _loggerFactory = loggerFactory;
    }

    public IScreenCapture CreateScreenCapture()
    {
        return new WindowsScreenCapture(_loggerFactory.CreateLogger<WindowsScreenCapture>());
    }

    public IInputSimulator CreateInputSimulator()
    {
        return new WindowsInputSimulator(_loggerFactory.CreateLogger<WindowsInputSimulator>());
    }

    public IApplicationController CreateApplicationController()
    {
        return new WindowsApplicationController(_loggerFactory.CreateLogger<WindowsApplicationController>());
    }

    public IComputerVisionService CreateComputerVisionService()
    {
        return new OpenCvComputerVisionService(_loggerFactory.CreateLogger<OpenCvComputerVisionService>());
    }

    public IOcrService CreateOcrService()
    {
        var engines = new List<IOcrEngine>
        {
            new TesseractOcrEngine(_loggerFactory.CreateLogger<TesseractOcrEngine>())
        };

        var visionService = CreateComputerVisionService();
        return new SmartOcrService(engines, visionService, _loggerFactory.CreateLogger<SmartOcrService>());
    }

    public string GetPlatformName()
    {
        return "Windows";
    }

    public bool IsPlatformSupported()
    {
        return OperatingSystem.IsWindows();
    }
}
