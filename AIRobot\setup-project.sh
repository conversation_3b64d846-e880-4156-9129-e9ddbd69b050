#!/bin/bash

# AIRobot 项目初始化脚本 (Bash 版本)
# 适用于 Linux/macOS 系统

echo "开始初始化 AIRobot 项目..."

# 检查 .NET 9 SDK
echo "检查 .NET 9 SDK..."
if ! command -v dotnet &> /dev/null; then
    echo "错误: 未找到 dotnet 命令"
    echo "请从 https://dotnet.microsoft.com/download 下载并安装 .NET 9 SDK"
    exit 1
fi

DOTNET_VERSION=$(dotnet --version)
if [[ ! $DOTNET_VERSION =~ ^9\. ]]; then
    echo "错误: 需要安装 .NET 9 SDK，当前版本: $DOTNET_VERSION"
    echo "请从 https://dotnet.microsoft.com/download 下载并安装"
    exit 1
fi
echo "✓ .NET SDK 版本: $DOTNET_VERSION"

# 检查 MAUI 工作负载
echo "检查 MAUI 工作负载..."
WORKLOADS=$(dotnet workload list)
if [[ ! $WORKLOADS =~ maui ]]; then
    echo "安装 MAUI 工作负载..."
    dotnet workload install maui
    if [ $? -ne 0 ]; then
        echo "错误: MAUI 工作负载安装失败"
        exit 1
    fi
fi
echo "✓ MAUI 工作负载已安装"

# 创建解决方案
echo "创建解决方案..."
if [ ! -f "AIRobot.sln" ]; then
    dotnet new sln -n AIRobot
    echo "✓ 解决方案创建完成"
else
    echo "解决方案文件已存在，跳过创建"
fi

# 创建目录结构
echo "创建目录结构..."
DIRECTORIES=(
    "src"
    "src/AIRobot.Core"
    "src/AIRobot.Platforms"
    "src/AIRobot.MAUI"
    "src/AIRobot.Shared"
    "tests"
    "tests/AIRobot.Core.Tests"
    "tests/AIRobot.Platforms.Tests"
    "docs"
    "samples"
)

for dir in "${DIRECTORIES[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo "✓ 创建目录: $dir"
    fi
done

# 创建核心类库
echo "创建核心类库..."
if [ ! -f "src/AIRobot.Core/AIRobot.Core.csproj" ]; then
    cd src/AIRobot.Core
    dotnet new classlib -n AIRobot.Core --force
    cd ../..
    dotnet sln add src/AIRobot.Core/AIRobot.Core.csproj
    echo "✓ 核心类库创建完成"
fi

# 创建平台实现类库
echo "创建平台实现类库..."
if [ ! -f "src/AIRobot.Platforms/AIRobot.Platforms.csproj" ]; then
    cd src/AIRobot.Platforms
    dotnet new classlib -n AIRobot.Platforms --force
    cd ../..
    dotnet sln add src/AIRobot.Platforms/AIRobot.Platforms.csproj
    echo "✓ 平台实现类库创建完成"
fi

# 创建 MAUI 项目
echo "创建 MAUI 项目..."
if [ ! -f "src/AIRobot.MAUI/AIRobot.MAUI.csproj" ]; then
    cd src/AIRobot.MAUI
    dotnet new maui-blazor -n AIRobot.MAUI --force
    cd ../..
    dotnet sln add src/AIRobot.MAUI/AIRobot.MAUI.csproj
    echo "✓ MAUI 项目创建完成"
fi

# 创建共享组件库
echo "创建共享组件库..."
if [ ! -f "src/AIRobot.Shared/AIRobot.Shared.csproj" ]; then
    cd src/AIRobot.Shared
    dotnet new razorclasslib -n AIRobot.Shared --force
    cd ../..
    dotnet sln add src/AIRobot.Shared/AIRobot.Shared.csproj
    echo "✓ 共享组件库创建完成"
fi

# 创建测试项目
echo "创建测试项目..."
if [ ! -f "tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj" ]; then
    cd tests/AIRobot.Core.Tests
    dotnet new xunit -n AIRobot.Core.Tests --force
    cd ../..
    dotnet sln add tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj
    echo "✓ 核心测试项目创建完成"
fi

if [ ! -f "tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj" ]; then
    cd tests/AIRobot.Platforms.Tests
    dotnet new xunit -n AIRobot.Platforms.Tests --force
    cd ../..
    dotnet sln add tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj
    echo "✓ 平台测试项目创建完成"
fi

# 添加项目引用
echo "配置项目引用..."

# AIRobot.Platforms 引用 AIRobot.Core
dotnet add src/AIRobot.Platforms/AIRobot.Platforms.csproj reference src/AIRobot.Core/AIRobot.Core.csproj

# AIRobot.MAUI 引用其他项目
dotnet add src/AIRobot.MAUI/AIRobot.MAUI.csproj reference src/AIRobot.Core/AIRobot.Core.csproj
dotnet add src/AIRobot.MAUI/AIRobot.MAUI.csproj reference src/AIRobot.Platforms/AIRobot.Platforms.csproj
dotnet add src/AIRobot.MAUI/AIRobot.MAUI.csproj reference src/AIRobot.Shared/AIRobot.Shared.csproj

# 测试项目引用
dotnet add tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj reference src/AIRobot.Core/AIRobot.Core.csproj
dotnet add tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj reference src/AIRobot.Platforms/AIRobot.Platforms.csproj

echo "✓ 项目引用配置完成"

# 添加 NuGet 包
echo "添加 NuGet 包..."

# 核心项目包
dotnet add src/AIRobot.Core/AIRobot.Core.csproj package Microsoft.Extensions.DependencyInjection.Abstractions
dotnet add src/AIRobot.Core/AIRobot.Core.csproj package Microsoft.Extensions.Logging.Abstractions
dotnet add src/AIRobot.Core/AIRobot.Core.csproj package System.Drawing.Common

# 平台项目包
dotnet add src/AIRobot.Platforms/AIRobot.Platforms.csproj package Tesseract
dotnet add src/AIRobot.Platforms/AIRobot.Platforms.csproj package SixLabors.ImageSharp
dotnet add src/AIRobot.Platforms/AIRobot.Platforms.csproj package System.Drawing.Common

# MAUI 项目包
dotnet add src/AIRobot.MAUI/AIRobot.MAUI.csproj package CommunityToolkit.Maui

# 测试项目包
dotnet add tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj package Moq
dotnet add tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj package FluentAssertions
dotnet add tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj package Moq
dotnet add tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj package FluentAssertions

echo "✓ NuGet 包添加完成"

# 还原依赖
echo "还原项目依赖..."
dotnet restore
if [ $? -eq 0 ]; then
    echo "✓ 依赖还原完成"
else
    echo "警告: 依赖还原可能存在问题"
fi

# 构建解决方案
echo "构建解决方案..."
dotnet build --no-restore
if [ $? -eq 0 ]; then
    echo "✓ 解决方案构建成功"
else
    echo "警告: 解决方案构建可能存在问题"
fi

echo ""
echo "🎉 AIRobot 项目初始化完成!"
echo ""
echo "下一步操作:"
echo "1. 查看文档: docs/Architecture.md"
echo "2. 查看开发指南: docs/Development-Guide.md"
echo "3. 查看代码示例: docs/Code-Examples.md"
echo "4. 开始开发: 在 IDE 中打开 AIRobot.sln"
echo ""
echo "运行测试: dotnet test"
echo "运行 MAUI 应用: dotnet run --project src/AIRobot.MAUI"
