# OCR 实现方案详细设计

## 1. 多引擎 OCR 架构

### 1.1 核心接口设计

```csharp
// src/AIRobot.Core/Interfaces/IOcrService.cs
namespace AIRobot.Core.Interfaces;

public interface IOcrService
{
    Task<OcrResult> RecognizeTextAsync(byte[] imageData, OcrEngine engine = OcrEngine.Auto);
    Task<IEnumerable<TextRegion>> FindTextAsync(byte[] imageData, string searchText);
    Task<bool> ContainsTextAsync(byte[] imageData, string searchText);
    Task<FormulaResult> RecognizeFormulaAsync(byte[] imageData);
    Task<TableResult> RecognizeTableAsync(byte[] imageData);
    Task SetLanguageAsync(string languageCode);
    Task<IEnumerable<OcrEngine>> GetAvailableEnginesAsync();
}

public enum OcrEngine
{
    Auto,           // 自动选择最佳引擎
    Tesseract,      // 传统 OCR，兼容性好
    PaddleOCR,      // 高精度中英文识别
    Surya,          // 专业公式和表格识别
    EasyOCR         // 备用方案
}
```

### 1.2 数据模型扩展

```csharp
// src/AIRobot.Core/Models/OcrModels.cs
namespace AIRobot.Core.Models;

public class OcrResult
{
    public string Text { get; set; } = string.Empty;
    public float Confidence { get; set; }
    public IEnumerable<TextRegion> Regions { get; set; } = new List<TextRegion>();
    public TimeSpan ProcessingTime { get; set; }
    public OcrEngine UsedEngine { get; set; }
    public string Language { get; set; } = "auto";
}

public class TextRegion
{
    public string Text { get; set; } = string.Empty;
    public Rectangle Bounds { get; set; }
    public float Confidence { get; set; }
    public int LineNumber { get; set; }
    public int WordIndex { get; set; }
    public IEnumerable<CharacterInfo> Characters { get; set; } = new List<CharacterInfo>();
}

public class CharacterInfo
{
    public char Character { get; set; }
    public Rectangle Bounds { get; set; }
    public float Confidence { get; set; }
}

public class FormulaResult
{
    public string LaTeX { get; set; } = string.Empty;
    public string MathML { get; set; } = string.Empty;
    public Rectangle Bounds { get; set; }
    public float Confidence { get; set; }
    public FormulaType Type { get; set; }
}

public enum FormulaType
{
    Inline,         // 行内公式
    Display,        // 独立公式
    Equation,       // 方程式
    Matrix,         // 矩阵
    Chemical        // 化学公式
}

public class TableResult
{
    public int Rows { get; set; }
    public int Columns { get; set; }
    public IEnumerable<TableCell> Cells { get; set; } = new List<TableCell>();
    public Rectangle Bounds { get; set; }
    public float Confidence { get; set; }
}

public class TableCell
{
    public string Text { get; set; } = string.Empty;
    public int Row { get; set; }
    public int Column { get; set; }
    public Rectangle Bounds { get; set; }
    public int RowSpan { get; set; } = 1;
    public int ColumnSpan { get; set; } = 1;
}
```

## 2. 具体引擎实现

### 2.1 PaddleOCR 实现 (推荐主引擎)

```csharp
// src/AIRobot.Platforms/Common/OCR/PaddleOcrEngine.cs
public class PaddleOcrEngine : IOcrEngine
{
    private readonly ILogger<PaddleOcrEngine> _logger;
    private readonly PythonProcessManager _pythonManager;
    private readonly string _paddleOcrPath;

    public PaddleOcrEngine(ILogger<PaddleOcrEngine> logger, PythonProcessManager pythonManager)
    {
        _logger = logger;
        _pythonManager = pythonManager;
        _paddleOcrPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "python", "paddleocr_wrapper.py");
    }

    public async Task<OcrResult> RecognizeTextAsync(byte[] imageData, string language = "auto")
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // 保存临时图片
            var tempImagePath = await SaveTempImageAsync(imageData);
            
            // 调用 Python 脚本
            var command = $"python \"{_paddleOcrPath}\" \"{tempImagePath}\" --lang {language}";
            var result = await _pythonManager.ExecuteAsync(command);
            
            // 解析结果
            var ocrResult = ParsePaddleOcrResult(result);
            ocrResult.ProcessingTime = stopwatch.Elapsed;
            ocrResult.UsedEngine = OcrEngine.PaddleOCR;
            
            // 清理临时文件
            File.Delete(tempImagePath);
            
            return ocrResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaddleOCR 识别失败");
            throw new OcrException("PaddleOCR 识别失败", ex);
        }
    }

    private OcrResult ParsePaddleOcrResult(string jsonResult)
    {
        var data = JsonSerializer.Deserialize<PaddleOcrResponse>(jsonResult);
        
        return new OcrResult
        {
            Text = string.Join("\n", data.Results.Select(r => r.Text)),
            Confidence = data.Results.Average(r => r.Confidence),
            Regions = data.Results.Select((r, index) => new TextRegion
            {
                Text = r.Text,
                Bounds = new Rectangle(
                    (int)r.Box[0][0], (int)r.Box[0][1],
                    (int)(r.Box[2][0] - r.Box[0][0]),
                    (int)(r.Box[2][1] - r.Box[0][1])
                ),
                Confidence = r.Confidence,
                LineNumber = index
            }).ToList()
        };
    }
}
```

### 2.2 Surya 公式识别实现

```csharp
// src/AIRobot.Platforms/Common/OCR/SuryaFormulaEngine.cs
public class SuryaFormulaEngine : IFormulaOcrEngine
{
    private readonly ILogger<SuryaFormulaEngine> _logger;
    private readonly PythonProcessManager _pythonManager;

    public async Task<FormulaResult> RecognizeFormulaAsync(byte[] imageData)
    {
        try
        {
            var tempImagePath = await SaveTempImageAsync(imageData);
            
            // 调用 Surya OCR 进行公式识别
            var command = $"python surya_formula.py \"{tempImagePath}\"";
            var result = await _pythonManager.ExecuteAsync(command);
            
            var formulaData = JsonSerializer.Deserialize<SuryaFormulaResponse>(result);
            
            return new FormulaResult
            {
                LaTeX = formulaData.LaTeX,
                MathML = formulaData.MathML,
                Bounds = ParseBounds(formulaData.BoundingBox),
                Confidence = formulaData.Confidence,
                Type = DetermineFormulaType(formulaData.LaTeX)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Surya 公式识别失败");
            throw new OcrException("公式识别失败", ex);
        }
    }

    private FormulaType DetermineFormulaType(string latex)
    {
        if (latex.Contains("\\begin{matrix}") || latex.Contains("\\begin{pmatrix}"))
            return FormulaType.Matrix;
        if (latex.Contains("\\ce{") || latex.Contains("\\chemfig"))
            return FormulaType.Chemical;
        if (latex.StartsWith("$$") && latex.EndsWith("$$"))
            return FormulaType.Display;
        return FormulaType.Inline;
    }
}
```

### 2.3 智能引擎选择器

```csharp
// src/AIRobot.Core/Services/SmartOcrService.cs
public class SmartOcrService : IOcrService
{
    private readonly Dictionary<OcrEngine, IOcrEngine> _engines;
    private readonly ILogger<SmartOcrService> _logger;

    public SmartOcrService(
        IEnumerable<IOcrEngine> engines,
        ILogger<SmartOcrService> logger)
    {
        _engines = engines.ToDictionary(e => e.EngineType, e => e);
        _logger = logger;
    }

    public async Task<OcrResult> RecognizeTextAsync(byte[] imageData, OcrEngine engine = OcrEngine.Auto)
    {
        if (engine == OcrEngine.Auto)
        {
            engine = await SelectBestEngineAsync(imageData);
        }

        if (!_engines.TryGetValue(engine, out var ocrEngine))
        {
            throw new NotSupportedException($"OCR 引擎 {engine} 不可用");
        }

        return await ocrEngine.RecognizeTextAsync(imageData);
    }

    private async Task<OcrEngine> SelectBestEngineAsync(byte[] imageData)
    {
        // 分析图像特征来选择最佳引擎
        var imageAnalysis = await AnalyzeImageAsync(imageData);

        // 如果检测到数学公式
        if (imageAnalysis.ContainsMathFormula)
        {
            return OcrEngine.Surya;
        }

        // 如果主要是中英文文本
        if (imageAnalysis.PrimaryLanguage == "zh" || imageAnalysis.PrimaryLanguage == "en")
        {
            return OcrEngine.PaddleOCR;
        }

        // 默认使用 Tesseract
        return OcrEngine.Tesseract;
    }

    private async Task<ImageAnalysis> AnalyzeImageAsync(byte[] imageData)
    {
        // 使用 OpenCV 进行图像分析
        using var mat = Mat.FromImageData(imageData);
        
        var analysis = new ImageAnalysis();
        
        // 检测数学符号
        analysis.ContainsMathFormula = await DetectMathSymbolsAsync(mat);
        
        // 检测主要语言
        analysis.PrimaryLanguage = await DetectPrimaryLanguageAsync(mat);
        
        // 检测表格结构
        analysis.ContainsTable = await DetectTableStructureAsync(mat);
        
        return analysis;
    }
}
```

## 3. Python 集成方案

### 3.1 Python 进程管理器

```csharp
// src/AIRobot.Platforms/Common/Python/PythonProcessManager.cs
public class PythonProcessManager : IDisposable
{
    private readonly string _pythonPath;
    private readonly string _workingDirectory;
    private readonly ILogger<PythonProcessManager> _logger;
    private readonly SemaphoreSlim _semaphore;

    public PythonProcessManager(ILogger<PythonProcessManager> logger)
    {
        _logger = logger;
        _pythonPath = FindPythonExecutable();
        _workingDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "python");
        _semaphore = new SemaphoreSlim(Environment.ProcessorCount); // 限制并发数
    }

    public async Task<string> ExecuteAsync(string script, string arguments = "", int timeoutMs = 30000)
    {
        await _semaphore.WaitAsync();
        
        try
        {
            using var process = new Process();
            process.StartInfo = new ProcessStartInfo
            {
                FileName = _pythonPath,
                Arguments = $"{script} {arguments}",
                WorkingDirectory = _workingDirectory,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };

            var output = new StringBuilder();
            var error = new StringBuilder();

            process.OutputDataReceived += (s, e) => { if (e.Data != null) output.AppendLine(e.Data); };
            process.ErrorDataReceived += (s, e) => { if (e.Data != null) error.AppendLine(e.Data); };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            var completed = await process.WaitForExitAsync(TimeSpan.FromMilliseconds(timeoutMs));
            
            if (!completed)
            {
                process.Kill();
                throw new TimeoutException($"Python 脚本执行超时: {script}");
            }

            if (process.ExitCode != 0)
            {
                throw new InvalidOperationException($"Python 脚本执行失败: {error}");
            }

            return output.ToString();
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private string FindPythonExecutable()
    {
        // 查找 Python 可执行文件
        var candidates = new[] { "python", "python3", "py" };
        
        foreach (var candidate in candidates)
        {
            try
            {
                using var process = Process.Start(new ProcessStartInfo
                {
                    FileName = candidate,
                    Arguments = "--version",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                });
                
                if (process != null && process.WaitForExit(5000) && process.ExitCode == 0)
                {
                    return candidate;
                }
            }
            catch
            {
                // 继续尝试下一个
            }
        }
        
        throw new FileNotFoundException("未找到 Python 可执行文件");
    }
}
```

### 3.2 Python 脚本示例

```python
# python/paddleocr_wrapper.py
import sys
import json
import argparse
from paddleocr import PaddleOCR
import cv2
import numpy as np

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('image_path', help='图片路径')
    parser.add_argument('--lang', default='ch', help='语言代码')
    args = parser.parse_args()
    
    try:
        # 初始化 PaddleOCR
        ocr = PaddleOCR(use_angle_cls=True, lang=args.lang, show_log=False)
        
        # 读取图片
        image = cv2.imread(args.image_path)
        
        # 执行 OCR
        results = ocr.ocr(image, cls=True)
        
        # 格式化结果
        formatted_results = []
        for line in results[0]:
            box, (text, confidence) = line
            formatted_results.append({
                'text': text,
                'confidence': float(confidence),
                'box': [[float(x), float(y)] for x, y in box]
            })
        
        # 输出 JSON 结果
        output = {
            'success': True,
            'results': formatted_results,
            'engine': 'PaddleOCR',
            'language': args.lang
        }
        
        print(json.dumps(output, ensure_ascii=False))
        
    except Exception as e:
        error_output = {
            'success': False,
            'error': str(e),
            'engine': 'PaddleOCR'
        }
        print(json.dumps(error_output, ensure_ascii=False))
        sys.exit(1)

if __name__ == '__main__':
    main()
```

## 4. 部署和配置

### 4.1 Python 环境配置脚本

```bash
# scripts/setup_python_env.sh
#!/bin/bash

echo "设置 Python OCR 环境..."

# 创建虚拟环境
python -m venv python_env
source python_env/bin/activate  # Linux/macOS
# python_env\Scripts\activate  # Windows

# 安装依赖
pip install paddlepaddle
pip install paddleocr
pip install surya-ocr
pip install opencv-python
pip install numpy

echo "Python OCR 环境设置完成"
```

### 4.2 配置文件

```json
// appsettings.json
{
  "OCR": {
    "DefaultEngine": "PaddleOCR",
    "Engines": {
      "PaddleOCR": {
        "Enabled": true,
        "Languages": ["ch", "en", "fr", "german", "korean", "japan"],
        "UseAngleCls": true,
        "UseGpu": false
      },
      "Tesseract": {
        "Enabled": true,
        "DataPath": "./tessdata",
        "Languages": ["eng", "chi_sim", "chi_tra"]
      },
      "Surya": {
        "Enabled": true,
        "ModelPath": "./models/surya"
      }
    },
    "PythonPath": "python",
    "ScriptPath": "./python",
    "TempPath": "./temp",
    "MaxConcurrency": 4,
    "TimeoutMs": 30000
  }
}
```

这个 OCR 实现方案提供了：

1. **多引擎支持**: PaddleOCR、Tesseract、Surya 等
2. **智能选择**: 根据图像内容自动选择最佳引擎
3. **公式识别**: 专门的数学公式识别能力
4. **详细位置信息**: 字符级别的位置数据
5. **跨平台兼容**: 通过 Python 进程调用实现跨平台
6. **高性能**: 进程池管理和并发控制
7. **易于扩展**: 插件化的引擎架构
