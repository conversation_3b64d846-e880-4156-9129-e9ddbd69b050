<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <UseWindowsForms>true</UseWindowsForms>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="QuickTest.cs" />
    <Compile Include="Models/CommonModels.cs" />
    <Compile Include="Platforms/Windows/Win32Api.cs" />
    <Compile Include="Platforms/Windows/WindowsScreenCapture.cs" />
    <Compile Include="Platforms/Windows/WindowsInputSimulator.cs" />
    <Compile Include="Interfaces/IScreenCapture.cs" />
    <Compile Include="Interfaces/IInputSimulator.cs" />
    <Compile Include="Exceptions/AIRobotExceptions.cs" />
  </ItemGroup>

</Project>
