# AIRobot 架构设计文档

## 1. 总体架构

### 1.1 架构原则

- **跨平台兼容**: 使用抽象接口隔离平台差异
- **模块化设计**: 功能模块独立，便于维护和扩展
- **工厂模式**: 根据平台动态创建具体实现
- **依赖注入**: 降低耦合度，提高可测试性
- **异步编程**: 提升用户体验和性能

### 1.2 分层架构

```
┌─────────────────────────────────────┐
│           Presentation Layer        │  Blazor MAUI UI
├─────────────────────────────────────┤
│           Application Layer         │  业务逻辑协调
├─────────────────────────────────────┤
│             Domain Layer            │  核心业务模型
├─────────────────────────────────────┤
│         Infrastructure Layer        │  平台特定实现
└─────────────────────────────────────┘
```

## 2. 核心模块设计

### 2.1 屏幕捕获模块 (IScreenCapture)

**职责**: 获取屏幕截图和窗口信息

```csharp
public interface IScreenCapture
{
    Task<byte[]> CaptureScreenAsync();
    Task<byte[]> CaptureWindowAsync(IntPtr windowHandle);
    Task<Rectangle> GetWindowBoundsAsync(IntPtr windowHandle);
    Task<IEnumerable<WindowInfo>> GetVisibleWindowsAsync();
}
```

**平台实现**:
- Windows: Win32 API (BitBlt, GetWindowRect)
- macOS: Core Graphics API
- Android: MediaProjection API

### 2.2 输入模拟模块 (IInputSimulator)

**职责**: 模拟鼠标和键盘操作

```csharp
public interface IInputSimulator
{
    Task ClickAsync(Point position, MouseButton button = MouseButton.Left);
    Task DoubleClickAsync(Point position);
    Task DragAsync(Point from, Point to);
    Task SendKeysAsync(string text);
    Task SendKeyAsync(VirtualKey key);
}
```

**平台实现**:
- Windows: SendInput API
- macOS: CGEvent API
- Android: AccessibilityService

### 2.3 计算机视觉模块 (IComputerVisionService)

**职责**: 综合计算机视觉处理，整合多种视觉技术

```csharp
public interface IComputerVisionService
{
    // 基础图像处理 (OpenCV)
    Task<byte[]> PreprocessImageAsync(byte[] imageData, ImageProcessingOptions options);
    Task<IEnumerable<ContourInfo>> DetectContoursAsync(byte[] imageData);
    Task<TemplateMatchResult> MatchTemplateAsync(byte[] image, byte[] template);

    // 智能分割 (SAM - 可选)
    Task<SegmentationResult> SegmentObjectAsync(byte[] imageData, Point clickPoint);
    Task<IEnumerable<ObjectSegment>> AutoSegmentAsync(byte[] imageData);
}
```

### 2.4 OCR 识别模块 (IOcrService)

**职责**: 多引擎文字识别和定位，支持公式识别

```csharp
public interface IOcrService
{
    Task<OcrResult> RecognizeTextAsync(byte[] imageData, OcrEngine engine = OcrEngine.PaddleOCR);
    Task<IEnumerable<TextRegion>> FindTextAsync(byte[] imageData, string searchText);
    Task<bool> ContainsTextAsync(byte[] imageData, string searchText);
    Task<FormulaResult> RecognizeFormulaAsync(byte[] imageData);
    Task<TableResult> RecognizeTableAsync(byte[] imageData);
    Task SetLanguageAsync(string languageCode);
}
```

### 2.5 应用程序控制模块 (IApplicationController)

**职责**: 启动和管理目标应用程序

```csharp
public interface IApplicationController
{
    Task<Process> LaunchApplicationAsync(string applicationPath);
    Task<bool> IsApplicationRunningAsync(string processName);
    Task CloseApplicationAsync(string processName);
    Task<IntPtr> FindWindowAsync(string windowTitle);
}
```

## 3. 工厂模式实现

### 3.1 平台服务工厂

```csharp
public interface IPlatformServiceFactory
{
    IScreenCapture CreateScreenCapture();
    IInputSimulator CreateInputSimulator();
    IApplicationController CreateApplicationController();
}

public class WindowsPlatformServiceFactory : IPlatformServiceFactory
{
    public IScreenCapture CreateScreenCapture() => new WindowsScreenCapture();
    public IInputSimulator CreateInputSimulator() => new WindowsInputSimulator();
    public IApplicationController CreateApplicationController() => new WindowsApplicationController();
}
```

### 3.2 服务注册

```csharp
public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddPlatformServices(this IServiceCollection services)
    {
#if WINDOWS
        services.AddSingleton<IPlatformServiceFactory, WindowsPlatformServiceFactory>();
#elif MACCATALYST
        services.AddSingleton<IPlatformServiceFactory, MacOsPlatformServiceFactory>();
#elif ANDROID
        services.AddSingleton<IPlatformServiceFactory, AndroidPlatformServiceFactory>();
#endif
        return services;
    }
}
```

## 4. 数据模型

### 4.1 核心实体

```csharp
public class WindowInfo
{
    public IntPtr Handle { get; set; }
    public string Title { get; set; }
    public string ProcessName { get; set; }
    public Rectangle Bounds { get; set; }
    public bool IsVisible { get; set; }
}

public class OcrResult
{
    public string Text { get; set; }
    public float Confidence { get; set; }
    public IEnumerable<TextRegion> Regions { get; set; }
}

public class TextRegion
{
    public string Text { get; set; }
    public Rectangle Bounds { get; set; }
    public float Confidence { get; set; }
}

public class AutomationTask
{
    public string Id { get; set; }
    public string Name { get; set; }
    public IEnumerable<AutomationStep> Steps { get; set; }
}

public abstract class AutomationStep
{
    public string Id { get; set; }
    public string Description { get; set; }
    public abstract Task ExecuteAsync(IAutomationContext context);
}
```

## 5. 错误处理和日志

### 5.1 异常处理策略

- 使用自定义异常类型
- 实现重试机制
- 提供详细的错误信息

### 5.2 日志记录

- 使用 Microsoft.Extensions.Logging
- 记录关键操作和错误
- 支持不同日志级别

## 6. 性能优化

### 6.1 异步编程

- 所有 I/O 操作使用异步方法
- 避免阻塞 UI 线程

### 6.2 资源管理

- 及时释放非托管资源
- 使用对象池减少 GC 压力

### 6.3 缓存策略

- 缓存屏幕截图
- 缓存 OCR 结果

## 7. 安全考虑

### 7.1 权限管理

- 请求必要的系统权限
- 用户授权确认

### 7.2 数据保护

- 敏感数据加密存储
- 安全的进程间通信

## 8. 测试策略

### 8.1 单元测试

- 核心业务逻辑测试
- Mock 平台相关依赖

### 8.2 集成测试

- 端到端自动化流程测试
- 平台兼容性测试

### 8.3 性能测试

- 响应时间测试
- 内存使用测试

## 9. 实际实现状态 (2024年12月)

### 9.1 已完成的功能 ✅

1. **核心接口定义**: 所有主要接口已定义完成
2. **Windows 平台实现**:
   - 屏幕捕获 (WindowsScreenCapture)
   - 输入模拟 (WindowsInputSimulator)
   - 应用程序控制 (WindowsApplicationController)
3. **OpenCV 集成**: 基础图像处理功能
4. **OCR 系统**: Tesseract 引擎和智能选择框架
5. **自动化引擎**: 任务执行和步骤管理
6. **单元测试**: 核心功能的测试覆盖

### 9.2 项目结构

```
AIRobot/
├── AIRobot.Core.csproj          # 核心业务逻辑
├── AIRobot.Platforms.csproj     # 平台特定实现
├── AIRobot.Console.csproj       # 控制台应用
├── AIRobot.Tests.csproj         # 单元测试
├── AIRobot.Simple.csproj        # 简化测试项目
├── Interfaces/                  # 核心接口
├── Models/                      # 数据模型
├── Services/                    # 核心服务
├── Platforms/                   # 平台实现
├── Tests/                       # 测试代码
└── docs/                        # 技术文档
```

### 9.3 运行方式

```bash
# 运行简单测试
dotnet run --project AIRobot.Simple.csproj

# 运行完整控制台应用
dotnet run --project AIRobot.Console.csproj

# 运行单元测试
dotnet test AIRobot.Tests.csproj
```

### 9.4 技术栈实现情况

| 组件 | 状态 | 实现方式 |
|------|------|----------|
| 屏幕捕获 | ✅ 完成 | Win32 API + System.Drawing |
| 输入模拟 | ✅ 完成 | Win32 SendInput API |
| 应用控制 | ✅ 完成 | Process + Win32 API |
| OpenCV | ✅ 完成 | OpenCvSharp4 |
| OCR | ✅ 基础完成 | Tesseract.NET |
| 自动化引擎 | ✅ 完成 | 自研任务执行框架 |
| 依赖注入 | ✅ 完成 | Microsoft.Extensions.DI |
| 日志系统 | ✅ 完成 | Microsoft.Extensions.Logging |
