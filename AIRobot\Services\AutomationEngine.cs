using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.Logging;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Core.Exceptions;

namespace AIRobot.Core.Services;

/// <summary>
/// 自动化任务引擎实现
/// </summary>
public class AutomationEngine : IAutomationEngine
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AutomationEngine> _logger;
    private readonly ConcurrentDictionary<string, TaskExecutionStatus> _taskStatuses = new();
    private readonly ConcurrentDictionary<string, CancellationTokenSource> _cancellationTokens = new();

    public event EventHandler<TaskStatusChangedEventArgs>? TaskStatusChanged;

    public AutomationEngine(IServiceProvider serviceProvider, ILogger<AutomationEngine> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task<TaskExecutionResult> ExecuteTaskAsync(AutomationTask task, CancellationToken cancellationToken = default)
    {
        var result = new TaskExecutionResult
        {
            StartTime = DateTime.Now
        };

        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("开始执行任务: {TaskName} (ID: {TaskId})", task.Name, task.Id);
            
            SetTaskStatus(task.Id, TaskExecutionStatus.Running);

            // 创建任务专用的取消令牌
            using var taskCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            _cancellationTokens[task.Id] = taskCts;

            // 创建自动化上下文
            var context = new AutomationContext(_serviceProvider, 
                _serviceProvider.GetRequiredService<ILogger<AutomationContext>>());

            // 按顺序执行步骤
            var enabledSteps = task.Steps.Where(s => s.IsEnabled).OrderBy(s => s.Order).ToList();
            
            foreach (var step in enabledSteps)
            {
                taskCts.Token.ThrowIfCancellationRequested();

                _logger.LogInformation("执行步骤: {StepName} (ID: {StepId})", step.Name, step.Id);

                var stepResult = await ExecuteStepAsync(step, context);
                result.StepResults.Add(stepResult);

                if (!stepResult.IsSuccess)
                {
                    _logger.LogError("步骤执行失败: {StepName}, 错误: {Error}", 
                        step.Name, stepResult.Message);
                    
                    result.IsSuccess = false;
                    result.Message = $"步骤 '{step.Name}' 执行失败: {stepResult.Message}";
                    break;
                }

                _logger.LogInformation("步骤执行成功: {StepName}, 耗时: {Duration}ms", 
                    step.Name, stepResult.ExecutionTime.TotalMilliseconds);
            }

            if (result.StepResults.All(r => r.IsSuccess))
            {
                result.IsSuccess = true;
                result.Message = "任务执行成功";
                SetTaskStatus(task.Id, TaskExecutionStatus.Completed);
            }
            else
            {
                SetTaskStatus(task.Id, TaskExecutionStatus.Failed);
            }
        }
        catch (OperationCanceledException)
        {
            result.IsSuccess = false;
            result.Message = "任务被取消";
            SetTaskStatus(task.Id, TaskExecutionStatus.Cancelled);
            _logger.LogWarning("任务被取消: {TaskName}", task.Name);
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.Message = $"任务执行异常: {ex.Message}";
            SetTaskStatus(task.Id, TaskExecutionStatus.Failed);
            _logger.LogError(ex, "任务执行异常: {TaskName}", task.Name);
        }
        finally
        {
            stopwatch.Stop();
            result.EndTime = DateTime.Now;
            result.TotalExecutionTime = stopwatch.Elapsed;
            
            _cancellationTokens.TryRemove(task.Id, out _);
            
            _logger.LogInformation("任务执行完成: {TaskName}, 成功: {Success}, 总耗时: {Duration}ms", 
                task.Name, result.IsSuccess, result.TotalExecutionTime.TotalMilliseconds);
        }

        return result;
    }

    public async Task<StepResult> ExecuteStepAsync(AutomationStep step, IAutomationContext context)
    {
        try
        {
            return await step.ExecuteAsync(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "步骤执行异常: {StepName}", step.Name);
            
            return new StepResult
            {
                IsSuccess = false,
                Message = $"步骤执行异常: {ex.Message}",
                Exception = ex
            };
        }
    }

    public Task PauseTaskAsync(string taskId)
    {
        SetTaskStatus(taskId, TaskExecutionStatus.Paused);
        _logger.LogInformation("任务已暂停: {TaskId}", taskId);
        return Task.CompletedTask;
    }

    public Task ResumeTaskAsync(string taskId)
    {
        SetTaskStatus(taskId, TaskExecutionStatus.Running);
        _logger.LogInformation("任务已恢复: {TaskId}", taskId);
        return Task.CompletedTask;
    }

    public Task CancelTaskAsync(string taskId)
    {
        if (_cancellationTokens.TryGetValue(taskId, out var cts))
        {
            cts.Cancel();
        }
        
        SetTaskStatus(taskId, TaskExecutionStatus.Cancelled);
        _logger.LogInformation("任务已取消: {TaskId}", taskId);
        return Task.CompletedTask;
    }

    public TaskExecutionStatus GetTaskStatus(string taskId)
    {
        return _taskStatuses.GetValueOrDefault(taskId, TaskExecutionStatus.NotStarted);
    }

    private void SetTaskStatus(string taskId, TaskExecutionStatus newStatus)
    {
        var oldStatus = _taskStatuses.GetValueOrDefault(taskId, TaskExecutionStatus.NotStarted);
        _taskStatuses[taskId] = newStatus;

        TaskStatusChanged?.Invoke(this, new TaskStatusChangedEventArgs
        {
            TaskId = taskId,
            OldStatus = oldStatus,
            NewStatus = newStatus,
            Message = $"任务状态从 {oldStatus} 变更为 {newStatus}"
        });
    }
}
