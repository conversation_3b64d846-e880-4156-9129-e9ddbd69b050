using System.Diagnostics;
using AIRobot.Core.Models;

namespace AIRobot.Core.Interfaces;

/// <summary>
/// 应用程序控制接口
/// </summary>
public interface IApplicationController
{
    /// <summary>
    /// 启动应用程序
    /// </summary>
    Task<Process> LaunchApplicationAsync(string applicationPath);
    
    /// <summary>
    /// 检查应用程序是否运行
    /// </summary>
    Task<bool> IsApplicationRunningAsync(string processName);
    
    /// <summary>
    /// 关闭应用程序
    /// </summary>
    Task CloseApplicationAsync(string processName);
    
    /// <summary>
    /// 查找窗口
    /// </summary>
    Task<IntPtr> FindWindowAsync(string windowTitle);
    
    /// <summary>
    /// 获取所有可见窗口
    /// </summary>
    Task<IEnumerable<WindowInfo>> GetVisibleWindowsAsync();
}
