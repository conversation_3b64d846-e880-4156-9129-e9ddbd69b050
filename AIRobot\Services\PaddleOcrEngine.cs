using System.Diagnostics;
using System.Text;
using System.Text.Json;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using Microsoft.Extensions.Logging;
using System.Drawing;

namespace AIRobot.Core.Services;

/// <summary>
/// PaddleOCR 引擎实现
/// 通过 Python 进程调用 PaddleOCR
/// </summary>
public class PaddleOcrEngine : IOcrEngine
{
    private readonly ILogger<PaddleOcrEngine> _logger;
    private readonly string _pythonPath;
    private readonly string _scriptPath;
    private readonly bool _isAvailable;

    public string Name => "PaddleOCR";
    public bool IsAvailable => _isAvailable;

    public PaddleOcrEngine(ILogger<PaddleOcrEngine> logger)
    {
        _logger = logger;
        _pythonPath = FindPythonPath();
        _scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "python_scripts", "paddleocr_service.py");
        _isAvailable = CheckAvailability();
    }

    public async Task<OcrResult> RecognizeTextAsync(byte[] imageData, string language = "auto")
    {
        if (!_isAvailable)
        {
            throw new InvalidOperationException("PaddleOCR 引擎不可用");
        }

        try
        {
            _logger.LogInformation("开始 PaddleOCR 文字识别");
            var startTime = DateTime.Now;

            // 保存临时图像文件
            var tempImagePath = Path.GetTempFileName() + ".png";
            await File.WriteAllBytesAsync(tempImagePath, imageData);

            try
            {
                // 调用 Python 脚本
                var result = await CallPythonScriptAsync(tempImagePath, language);
                var processingTime = DateTime.Now - startTime;

                _logger.LogInformation($"PaddleOCR 识别完成，耗时: {processingTime.TotalMilliseconds}ms");
                
                return new OcrResult
                {
                    Text = result.Text,
                    Confidence = result.Confidence,
                    Regions = result.Regions,
                    ProcessingTime = processingTime,
                    UsedEngine = Name,
                    Language = language
                };
            }
            finally
            {
                // 清理临时文件
                try { File.Delete(tempImagePath); } catch { }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaddleOCR 识别失败");
            throw;
        }
    }

    public async Task<OcrResult> RecognizeFormulaAsync(byte[] imageData)
    {
        // PaddleOCR 支持公式识别，使用特殊配置
        return await RecognizeTextAsync(imageData, "formula");
    }

    public async Task<OcrResult> RecognizeTableAsync(byte[] imageData)
    {
        // PaddleOCR 支持表格识别
        return await RecognizeTextAsync(imageData, "table");
    }

    public Task SetLanguageAsync(string language)
    {
        // PaddleOCR 支持动态语言设置
        _logger.LogInformation($"设置 PaddleOCR 语言: {language}");
        return Task.CompletedTask;
    }

    private async Task<OcrResult> CallPythonScriptAsync(string imagePath, string language)
    {
        try
        {
            // 确保 Python 脚本存在
            await EnsurePythonScriptAsync();

            var startInfo = new ProcessStartInfo
            {
                FileName = _pythonPath,
                Arguments = $"\"{_scriptPath}\" \"{imagePath}\" \"{language}\"",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                StandardOutputEncoding = Encoding.UTF8,
                StandardErrorEncoding = Encoding.UTF8
            };

            using var process = new Process { StartInfo = startInfo };
            process.Start();

            var outputTask = process.StandardOutput.ReadToEndAsync();
            var errorTask = process.StandardError.ReadToEndAsync();

            await process.WaitForExitAsync();

            var output = await outputTask;
            var error = await errorTask;

            if (process.ExitCode != 0)
            {
                throw new InvalidOperationException($"Python 脚本执行失败: {error}");
            }

            // 解析 JSON 结果
            var jsonResult = JsonSerializer.Deserialize<PaddleOcrResult>(output);
            if (jsonResult == null)
            {
                throw new InvalidOperationException("无法解析 PaddleOCR 结果");
            }

            return ConvertToOcrResult(jsonResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "调用 Python 脚本失败");
            throw;
        }
    }

    private async Task EnsurePythonScriptAsync()
    {
        var scriptDir = Path.GetDirectoryName(_scriptPath);
        if (!Directory.Exists(scriptDir))
        {
            Directory.CreateDirectory(scriptDir!);
        }

        if (!File.Exists(_scriptPath))
        {
            await CreatePythonScriptAsync();
        }
    }

    private async Task CreatePythonScriptAsync()
    {
        var scriptContent = @"#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import json
import cv2
import numpy as np
from paddleocr import PaddleOCR
import traceback

def main():
    if len(sys.argv) != 3:
        print(json.dumps({""error"": ""Usage: script.py <image_path> <language>""}))
        sys.exit(1)
    
    image_path = sys.argv[1]
    language = sys.argv[2]
    
    try:
        # 初始化 PaddleOCR
        if language == ""formula"":
            ocr = PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=False, show_log=False)
        elif language == ""table"":
            ocr = PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=False, show_log=False)
        else:
            lang_map = {
                ""auto"": ""ch"",
                ""chinese"": ""ch"",
                ""english"": ""en"",
                ""japanese"": ""japan"",
                ""korean"": ""korean""
            }
            ocr_lang = lang_map.get(language, ""ch"")
            ocr = PaddleOCR(use_angle_cls=True, lang=ocr_lang, use_gpu=False, show_log=False)
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f""Cannot read image: {image_path}"")
        
        # 执行 OCR
        results = ocr.ocr(image, cls=True)
        
        # 处理结果
        text_lines = []
        regions = []
        total_confidence = 0
        count = 0
        
        if results and results[0]:
            for line in results[0]:
                if line:
                    bbox = line[0]
                    text_info = line[1]
                    text = text_info[0]
                    confidence = text_info[1]
                    
                    text_lines.append(text)
                    total_confidence += confidence
                    count += 1
                    
                    # 计算边界框
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    x = int(min(x_coords))
                    y = int(min(y_coords))
                    width = int(max(x_coords) - min(x_coords))
                    height = int(max(y_coords) - min(y_coords))
                    
                    regions.append({
                        ""text"": text,
                        ""confidence"": confidence,
                        ""bounds"": {
                            ""x"": x,
                            ""y"": y,
                            ""width"": width,
                            ""height"": height
                        }
                    })
        
        # 构建结果
        result = {
            ""text"": ""\n"".join(text_lines),
            ""confidence"": total_confidence / count if count > 0 else 0,
            ""regions"": regions,
            ""success"": True
        }
        
        print(json.dumps(result, ensure_ascii=False))
        
    except Exception as e:
        error_result = {
            ""error"": str(e),
            ""traceback"": traceback.format_exc(),
            ""success"": False
        }
        print(json.dumps(error_result, ensure_ascii=False))
        sys.exit(1)

if __name__ == ""__main__"":
    main()
";

        await File.WriteAllTextAsync(_scriptPath, scriptContent, Encoding.UTF8);
        _logger.LogInformation($"创建 Python 脚本: {_scriptPath}");
    }

    private OcrResult ConvertToOcrResult(PaddleOcrResult paddleResult)
    {
        var regions = paddleResult.Regions?.Select(r => new TextRegion
        {
            Text = r.Text,
            Confidence = r.Confidence,
            Bounds = new Rectangle(r.Bounds.X, r.Bounds.Y, r.Bounds.Width, r.Bounds.Height)
        }).ToList() ?? new List<TextRegion>();

        return new OcrResult
        {
            Text = paddleResult.Text ?? "",
            Confidence = paddleResult.Confidence,
            Regions = regions,
            ProcessingTime = TimeSpan.Zero,
            UsedEngine = Name,
            Language = "auto"
        };
    }

    private string FindPythonPath()
    {
        // 首先尝试虚拟环境中的 Python
        var venvPython = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "python_env", 
            OperatingSystem.IsWindows() ? "Scripts\\python.exe" : "bin/python");
        
        if (File.Exists(venvPython))
        {
            return venvPython;
        }

        // 尝试系统 Python
        var pythonCommands = OperatingSystem.IsWindows() 
            ? new[] { "python", "python3", "py" }
            : new[] { "python3", "python" };

        foreach (var cmd in pythonCommands)
        {
            try
            {
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = cmd,
                    Arguments = "--version",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    process.WaitForExit();
                    if (process.ExitCode == 0)
                    {
                        return cmd;
                    }
                }
            }
            catch
            {
                // 忽略错误，继续尝试下一个
            }
        }

        return "python";
    }

    private bool CheckAvailability()
    {
        try
        {
            var process = Process.Start(new ProcessStartInfo
            {
                FileName = _pythonPath,
                Arguments = "-c \"import paddleocr; print('OK')\"",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            });

            if (process != null)
            {
                process.WaitForExit();
                return process.ExitCode == 0;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "PaddleOCR 可用性检查失败");
        }

        return false;
    }

    private class PaddleOcrResult
    {
        public string? Text { get; set; }
        public double Confidence { get; set; }
        public List<PaddleOcrRegion>? Regions { get; set; }
        public bool Success { get; set; }
        public string? Error { get; set; }
    }

    private class PaddleOcrRegion
    {
        public string Text { get; set; } = "";
        public double Confidence { get; set; }
        public PaddleOcrBounds Bounds { get; set; } = new();
    }

    private class PaddleOcrBounds
    {
        public int X { get; set; }
        public int Y { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
    }
}
