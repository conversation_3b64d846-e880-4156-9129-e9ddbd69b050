using System.Drawing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using AIRobot.Core.Extensions;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Core.Models.Steps;
using AIRobot.SimpleTest;

namespace AIRobot.Console;

class Program
{
    static async Task Main(string[] args)
    {
        System.Console.WriteLine("🤖 AIRobot 跨平台自动化机器人");
        System.Console.WriteLine("================================");

        // 首先运行简单测试
        System.Console.WriteLine("\n🧪 运行简单测试...");
        await SimpleTest.RunTestsAsync();

        System.Console.WriteLine("\n" + new string('=', 50));
        System.Console.WriteLine("🚀 启动完整系统...");

        // 创建主机
        var host = Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                services.AddAIRobot();
            })
            .Build();

        try
        {
            // 获取服务
            var logger = host.Services.GetRequiredService<ILogger<Program>>();
            var platformFactory = host.Services.GetRequiredService<IPlatformServiceFactory>();
            var automationEngine = host.Services.GetRequiredService<IAutomationEngine>();

            logger.LogInformation("AIRobot 启动成功");
            logger.LogInformation("当前平台: {Platform}", platformFactory.GetPlatformName());
            logger.LogInformation("平台支持: {Supported}", platformFactory.IsPlatformSupported());

            // 显示菜单
            await ShowMenuAsync(host.Services, logger);
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ 启动失败: {ex.Message}");
            System.Console.WriteLine($"详细错误: {ex}");

            // 如果完整系统启动失败，至少简单测试已经运行了
            System.Console.WriteLine("\n💡 提示: 简单测试已完成，可以查看上面的结果");
        }
        finally
        {
            await host.StopAsync();
        }
    }

    static async Task ShowMenuAsync(IServiceProvider services, ILogger logger)
    {
        while (true)
        {
            System.Console.WriteLine("\n📋 请选择功能:");
            System.Console.WriteLine("1. 测试屏幕捕获");
            System.Console.WriteLine("2. 测试输入模拟");
            System.Console.WriteLine("3. 测试 OCR 识别");
            System.Console.WriteLine("4. 测试计算机视觉");
            System.Console.WriteLine("5. 运行自动化任务");
            System.Console.WriteLine("6. 获取窗口列表");
            System.Console.WriteLine("0. 退出");
            System.Console.Write("\n请输入选择 (0-6): ");

            var choice = System.Console.ReadLine();

            try
            {
                switch (choice)
                {
                    case "1":
                        await TestScreenCaptureAsync(services, logger);
                        break;
                    case "2":
                        await TestInputSimulationAsync(services, logger);
                        break;
                    case "3":
                        await TestOcrAsync(services, logger);
                        break;
                    case "4":
                        await TestComputerVisionAsync(services, logger);
                        break;
                    case "5":
                        await RunAutomationTaskAsync(services, logger);
                        break;
                    case "6":
                        await GetWindowListAsync(services, logger);
                        break;
                    case "0":
                        System.Console.WriteLine("👋 再见!");
                        return;
                    default:
                        System.Console.WriteLine("❌ 无效选择，请重试");
                        break;
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "操作执行失败");
                System.Console.WriteLine($"❌ 操作失败: {ex.Message}");
            }

            System.Console.WriteLine("\n按任意键继续...");
            System.Console.ReadKey();
        }
    }

    static async Task TestScreenCaptureAsync(IServiceProvider services, ILogger logger)
    {
        System.Console.WriteLine("\n📸 测试屏幕捕获...");
        
        var screenCapture = services.GetRequiredService<IScreenCapture>();
        
        var imageData = await screenCapture.CaptureScreenAsync();
        System.Console.WriteLine($"✅ 屏幕捕获成功，图像大小: {imageData.Length} bytes");
        
        // 保存截图
        var fileName = $"screenshot_{DateTime.Now:yyyyMMdd_HHmmss}.png";
        await File.WriteAllBytesAsync(fileName, imageData);
        System.Console.WriteLine($"📁 截图已保存: {fileName}");
    }

    static async Task TestInputSimulationAsync(IServiceProvider services, ILogger logger)
    {
        System.Console.WriteLine("\n🖱️ 测试输入模拟...");
        System.Console.WriteLine("将在 3 秒后在屏幕中央点击");
        
        await Task.Delay(3000);
        
        var inputSimulator = services.GetRequiredService<IInputSimulator>();
        var screenBounds = Screen.PrimaryScreen.Bounds;
        var centerPoint = new Point(screenBounds.Width / 2, screenBounds.Height / 2);
        
        await inputSimulator.ClickAsync(centerPoint);
        System.Console.WriteLine($"✅ 点击完成，位置: ({centerPoint.X}, {centerPoint.Y})");
    }

    static async Task TestOcrAsync(IServiceProvider services, ILogger logger)
    {
        System.Console.WriteLine("\n🔍 测试 OCR 识别...");
        
        var screenCapture = services.GetRequiredService<IScreenCapture>();
        var ocrService = services.GetRequiredService<IOcrService>();
        
        System.Console.WriteLine("正在捕获屏幕...");
        var imageData = await screenCapture.CaptureScreenAsync();
        
        System.Console.WriteLine("正在进行 OCR 识别...");
        var result = await ocrService.RecognizeTextAsync(imageData);
        
        System.Console.WriteLine($"✅ OCR 识别完成");
        System.Console.WriteLine($"引擎: {result.UsedEngine}");
        System.Console.WriteLine($"置信度: {result.Confidence:P2}");
        System.Console.WriteLine($"处理时间: {result.ProcessingTime.TotalMilliseconds:F0}ms");
        System.Console.WriteLine($"识别的文字区域数: {result.Regions.Count()}");
        
        if (!string.IsNullOrEmpty(result.Text))
        {
            System.Console.WriteLine($"识别的文字 (前200字符):");
            var preview = result.Text.Length > 200 ? result.Text.Substring(0, 200) + "..." : result.Text;
            System.Console.WriteLine(preview);
        }
    }

    static async Task TestComputerVisionAsync(IServiceProvider services, ILogger logger)
    {
        System.Console.WriteLine("\n👁️ 测试计算机视觉...");
        
        var screenCapture = services.GetRequiredService<IScreenCapture>();
        var visionService = services.GetRequiredService<IComputerVisionService>();
        
        System.Console.WriteLine("正在捕获屏幕...");
        var imageData = await screenCapture.CaptureScreenAsync();
        
        System.Console.WriteLine("正在检测轮廓...");
        var contours = await visionService.DetectContoursAsync(imageData);
        
        System.Console.WriteLine($"✅ 轮廓检测完成，找到 {contours.Count()} 个轮廓");
        
        foreach (var contour in contours.Take(5))
        {
            System.Console.WriteLine($"  轮廓: 面积={contour.Area:F0}, 周长={contour.Perimeter:F0}, 边界={contour.BoundingRect}");
        }
    }

    static async Task RunAutomationTaskAsync(IServiceProvider services, ILogger logger)
    {
        System.Console.WriteLine("\n🚀 运行自动化任务...");
        
        var automationEngine = services.GetRequiredService<IAutomationEngine>();
        
        // 创建一个简单的测试任务
        var task = new AutomationTask
        {
            Name = "测试任务",
            Description = "简单的自动化测试任务",
            Steps = new List<AutomationStep>
            {
                new ClickStep
                {
                    Name = "点击屏幕中央",
                    Description = "在屏幕中央点击",
                    Position = new Point(Screen.PrimaryScreen.Bounds.Width / 2, Screen.PrimaryScreen.Bounds.Height / 2),
                    Order = 1,
                    Delay = TimeSpan.FromSeconds(1)
                },
                new TypeTextStep
                {
                    Name = "输入测试文字",
                    Description = "输入一些测试文字",
                    Text = "Hello AIRobot!",
                    Order = 2,
                    Delay = TimeSpan.FromSeconds(1)
                }
            }
        };
        
        System.Console.WriteLine($"任务: {task.Name}");
        System.Console.WriteLine($"步骤数: {task.Steps.Count}");
        System.Console.WriteLine("将在 3 秒后开始执行...");
        
        await Task.Delay(3000);
        
        var result = await automationEngine.ExecuteTaskAsync(task);
        
        System.Console.WriteLine($"✅ 任务执行完成");
        System.Console.WriteLine($"成功: {result.IsSuccess}");
        System.Console.WriteLine($"消息: {result.Message}");
        System.Console.WriteLine($"总耗时: {result.TotalExecutionTime.TotalMilliseconds:F0}ms");
        System.Console.WriteLine($"步骤结果:");
        
        foreach (var stepResult in result.StepResults)
        {
            System.Console.WriteLine($"  - {(stepResult.IsSuccess ? "✅" : "❌")} {stepResult.Message} ({stepResult.ExecutionTime.TotalMilliseconds:F0}ms)");
        }
    }

    static async Task GetWindowListAsync(IServiceProvider services, ILogger logger)
    {
        System.Console.WriteLine("\n🪟 获取窗口列表...");
        
        var screenCapture = services.GetRequiredService<IScreenCapture>();
        var windows = await screenCapture.GetVisibleWindowsAsync();
        
        System.Console.WriteLine($"✅ 找到 {windows.Count()} 个可见窗口:");
        
        foreach (var window in windows.Take(10))
        {
            System.Console.WriteLine($"  - {window.Title} ({window.ProcessName}) [{window.Bounds.Width}x{window.Bounds.Height}]");
        }
        
        if (windows.Count() > 10)
        {
            System.Console.WriteLine($"  ... 还有 {windows.Count() - 10} 个窗口");
        }
    }
}
