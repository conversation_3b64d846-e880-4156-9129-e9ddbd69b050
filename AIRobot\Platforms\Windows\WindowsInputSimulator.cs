using System.Drawing;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Logging;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Core.Exceptions;

namespace AIRobot.Platforms.Windows;

/// <summary>
/// Windows 输入模拟实现
/// </summary>
public class WindowsInputSimulator : IInputSimulator
{
    private readonly ILogger<WindowsInputSimulator> _logger;

    public WindowsInputSimulator(ILogger<WindowsInputSimulator> logger)
    {
        _logger = logger;
    }

    public async Task ClickAsync(Point position, MouseButton button = MouseButton.Left)
    {
        await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("执行点击: 位置({X}, {Y}), 按钮: {Button}", position.X, position.Y, button);

                Win32Api.SetCursorPos(position.X, position.Y);
                Thread.Sleep(10); // 短暂延迟确保鼠标位置设置完成

                var inputs = new INPUT[2];
                inputs[0] = CreateMouseInput(GetMouseDownFlag(button));
                inputs[1] = CreateMouseInput(GetMouseUpFlag(button));

                var result = Win32Api.SendInput((uint)inputs.Length, inputs, Marshal.SizeOf<INPUT>());
                
                if (result != (uint)inputs.Length)
                {
                    throw new InputSimulationException($"SendInput 失败，预期发送 {inputs.Length} 个输入，实际发送 {result} 个");
                }

                _logger.LogDebug("点击完成: 位置({X}, {Y})", position.X, position.Y);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "点击操作失败: 位置({X}, {Y})", position.X, position.Y);
                throw new InputSimulationException("点击操作失败", ex);
            }
        });
    }

    public async Task DoubleClickAsync(Point position)
    {
        try
        {
            _logger.LogDebug("执行双击: 位置({X}, {Y})", position.X, position.Y);

            await ClickAsync(position, MouseButton.Left);
            await Task.Delay(50); // 双击间隔
            await ClickAsync(position, MouseButton.Left);

            _logger.LogDebug("双击完成: 位置({X}, {Y})", position.X, position.Y);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "双击操作失败: 位置({X}, {Y})", position.X, position.Y);
            throw new InputSimulationException("双击操作失败", ex);
        }
    }

    public async Task DragAsync(Point from, Point to)
    {
        await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("执行拖拽: 从({FromX}, {FromY}) 到({ToX}, {ToY})", 
                    from.X, from.Y, to.X, to.Y);

                // 移动到起始位置
                Win32Api.SetCursorPos(from.X, from.Y);
                Thread.Sleep(10);

                // 按下鼠标左键
                var mouseDown = CreateMouseInput(Win32Api.MOUSEEVENTF_LEFTDOWN);
                Win32Api.SendInput(1, new[] { mouseDown }, Marshal.SizeOf<INPUT>());
                Thread.Sleep(10);

                // 移动到目标位置
                Win32Api.SetCursorPos(to.X, to.Y);
                Thread.Sleep(10);

                // 释放鼠标左键
                var mouseUp = CreateMouseInput(Win32Api.MOUSEEVENTF_LEFTUP);
                Win32Api.SendInput(1, new[] { mouseUp }, Marshal.SizeOf<INPUT>());

                _logger.LogDebug("拖拽完成: 从({FromX}, {FromY}) 到({ToX}, {ToY})", 
                    from.X, from.Y, to.X, to.Y);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "拖拽操作失败: 从({FromX}, {FromY}) 到({ToX}, {ToY})", 
                    from.X, from.Y, to.X, to.Y);
                throw new InputSimulationException("拖拽操作失败", ex);
            }
        });
    }

    public async Task SendKeysAsync(string text)
    {
        await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("发送文本: '{Text}'", text);

                var inputs = new List<INPUT>();

                foreach (char c in text)
                {
                    var vk = Win32Api.VkKeyScan(c);
                    var virtualKey = (ushort)(vk & 0xFF);
                    var shiftState = (vk & 0x100) != 0;

                    if (shiftState)
                    {
                        // 按下 Shift
                        inputs.Add(CreateKeyboardInput((ushort)VirtualKey.Shift, false));
                    }

                    // 按下字符键
                    inputs.Add(CreateKeyboardInput(virtualKey, false));
                    // 释放字符键
                    inputs.Add(CreateKeyboardInput(virtualKey, true));

                    if (shiftState)
                    {
                        // 释放 Shift
                        inputs.Add(CreateKeyboardInput((ushort)VirtualKey.Shift, true));
                    }
                }

                if (inputs.Count > 0)
                {
                    var result = Win32Api.SendInput((uint)inputs.Count, inputs.ToArray(), Marshal.SizeOf<INPUT>());
                    
                    if (result != (uint)inputs.Count)
                    {
                        throw new InputSimulationException($"SendInput 失败，预期发送 {inputs.Count} 个输入，实际发送 {result} 个");
                    }
                }

                _logger.LogDebug("文本发送完成: '{Text}'", text);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文本发送失败: '{Text}'", text);
                throw new InputSimulationException("文本发送失败", ex);
            }
        });
    }

    public async Task SendKeyAsync(VirtualKey key)
    {
        await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("发送按键: {Key}", key);

                var inputs = new INPUT[2];
                inputs[0] = CreateKeyboardInput((ushort)key, false); // 按下
                inputs[1] = CreateKeyboardInput((ushort)key, true);  // 释放

                var result = Win32Api.SendInput((uint)inputs.Length, inputs, Marshal.SizeOf<INPUT>());
                
                if (result != (uint)inputs.Length)
                {
                    throw new InputSimulationException($"SendInput 失败，预期发送 {inputs.Length} 个输入，实际发送 {result} 个");
                }

                _logger.LogDebug("按键发送完成: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按键发送失败: {Key}", key);
                throw new InputSimulationException("按键发送失败", ex);
            }
        });
    }

    public async Task SendKeyComboAsync(params VirtualKey[] keys)
    {
        await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("发送组合键: {Keys}", string.Join("+", keys));

                var inputs = new List<INPUT>();

                // 按下所有键
                foreach (var key in keys)
                {
                    inputs.Add(CreateKeyboardInput((ushort)key, false));
                }

                // 释放所有键（逆序）
                foreach (var key in keys.Reverse())
                {
                    inputs.Add(CreateKeyboardInput((ushort)key, true));
                }

                var result = Win32Api.SendInput((uint)inputs.Count, inputs.ToArray(), Marshal.SizeOf<INPUT>());
                
                if (result != (uint)inputs.Count)
                {
                    throw new InputSimulationException($"SendInput 失败，预期发送 {inputs.Count} 个输入，实际发送 {result} 个");
                }

                _logger.LogDebug("组合键发送完成: {Keys}", string.Join("+", keys));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "组合键发送失败: {Keys}", string.Join("+", keys));
                throw new InputSimulationException("组合键发送失败", ex);
            }
        });
    }

    private INPUT CreateMouseInput(uint flags)
    {
        return new INPUT
        {
            type = Win32Api.INPUT_MOUSE,
            u = new InputUnion
            {
                mi = new MOUSEINPUT
                {
                    dwFlags = flags,
                    time = 0,
                    dwExtraInfo = IntPtr.Zero
                }
            }
        };
    }

    private INPUT CreateKeyboardInput(ushort vk, bool keyUp)
    {
        return new INPUT
        {
            type = Win32Api.INPUT_KEYBOARD,
            u = new InputUnion
            {
                ki = new KEYBDINPUT
                {
                    wVk = vk,
                    wScan = 0,
                    dwFlags = keyUp ? Win32Api.KEYEVENTF_KEYUP : 0,
                    time = 0,
                    dwExtraInfo = IntPtr.Zero
                }
            }
        };
    }

    private uint GetMouseDownFlag(MouseButton button)
    {
        return button switch
        {
            MouseButton.Left => Win32Api.MOUSEEVENTF_LEFTDOWN,
            MouseButton.Right => Win32Api.MOUSEEVENTF_RIGHTDOWN,
            MouseButton.Middle => Win32Api.MOUSEEVENTF_MIDDLEDOWN,
            _ => Win32Api.MOUSEEVENTF_LEFTDOWN
        };
    }

    private uint GetMouseUpFlag(MouseButton button)
    {
        return button switch
        {
            MouseButton.Left => Win32Api.MOUSEEVENTF_LEFTUP,
            MouseButton.Right => Win32Api.MOUSEEVENTF_RIGHTUP,
            MouseButton.Middle => Win32Api.MOUSEEVENTF_MIDDLEUP,
            _ => Win32Api.MOUSEEVENTF_LEFTUP
        };
    }
}
