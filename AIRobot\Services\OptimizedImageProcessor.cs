using Microsoft.Extensions.Logging;
using System.Buffers;
using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;

namespace AIRobot.Core.Services;

/// <summary>
/// 优化的图像处理服务
/// 使用内存池和高效算法减少内存分配和提升性能
/// </summary>
public class OptimizedImageProcessor : IDisposable
{
    private readonly ILogger<OptimizedImageProcessor> _logger;
    private readonly ArrayPool<byte> _bytePool;
    private readonly object _lockObject = new();
    private bool _disposed = false;

    public OptimizedImageProcessor(ILogger<OptimizedImageProcessor> logger)
    {
        _logger = logger;
        _bytePool = ArrayPool<byte>.Shared;
    }

    /// <summary>
    /// 高效的图像格式转换
    /// </summary>
    public async Task<byte[]> ConvertImageFormatAsync(byte[] imageData, ImageFormat targetFormat)
    {
        if (imageData == null || imageData.Length == 0)
            throw new ArgumentException("图像数据不能为空", nameof(imageData));

        try
        {
            using var inputStream = new MemoryStream(imageData);
            using var outputStream = new MemoryStream();
            
            // 使用异步操作避免阻塞
            await Task.Run(() =>
            {
                using var image = Image.FromStream(inputStream);
                image.Save(outputStream, targetFormat);
            });

            return outputStream.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像格式转换失败");
            throw;
        }
    }

    /// <summary>
    /// 内存优化的图像缩放
    /// </summary>
    public async Task<byte[]> ResizeImageAsync(byte[] imageData, int targetWidth, int targetHeight, bool maintainAspectRatio = true)
    {
        if (imageData == null || imageData.Length == 0)
            throw new ArgumentException("图像数据不能为空", nameof(imageData));

        try
        {
            return await Task.Run(() =>
            {
                using var inputStream = new MemoryStream(imageData);
                using var originalImage = Image.FromStream(inputStream);
                
                // 计算目标尺寸
                var (newWidth, newHeight) = CalculateTargetSize(
                    originalImage.Width, originalImage.Height, 
                    targetWidth, targetHeight, maintainAspectRatio);

                // 使用高质量缩放
                using var resizedImage = new Bitmap(newWidth, newHeight);
                using var graphics = Graphics.FromImage(resizedImage);
                
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                
                graphics.DrawImage(originalImage, 0, 0, newWidth, newHeight);

                using var outputStream = new MemoryStream();
                resizedImage.Save(outputStream, ImageFormat.Png);
                return outputStream.ToArray();
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像缩放失败");
            throw;
        }
    }

    /// <summary>
    /// 批量图像处理（并行优化）
    /// </summary>
    public async Task<List<byte[]>> ProcessImagesInBatchAsync(
        IEnumerable<byte[]> images, 
        Func<byte[], Task<byte[]>> processor,
        int maxConcurrency = Environment.ProcessorCount)
    {
        var imageList = images.ToList();
        if (!imageList.Any())
            return new List<byte[]>();

        try
        {
            var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
            var tasks = imageList.Select(async imageData =>
            {
                await semaphore.WaitAsync();
                try
                {
                    return await processor(imageData);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var results = await Task.WhenAll(tasks);
            return results.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量图像处理失败");
            throw;
        }
    }

    /// <summary>
    /// 内存高效的图像压缩
    /// </summary>
    public async Task<byte[]> CompressImageAsync(byte[] imageData, long qualityLevel = 75L)
    {
        if (imageData == null || imageData.Length == 0)
            throw new ArgumentException("图像数据不能为空", nameof(imageData));

        try
        {
            return await Task.Run(() =>
            {
                using var inputStream = new MemoryStream(imageData);
                using var image = Image.FromStream(inputStream);
                
                // 设置 JPEG 压缩参数
                var jpegCodec = ImageCodecInfo.GetImageDecoders()
                    .FirstOrDefault(codec => codec.FormatID == ImageFormat.Jpeg.Guid);
                
                if (jpegCodec == null)
                    throw new NotSupportedException("JPEG 编码器不可用");

                var encoderParams = new EncoderParameters(1);
                encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, qualityLevel);

                using var outputStream = new MemoryStream();
                image.Save(outputStream, jpegCodec, encoderParams);
                return outputStream.ToArray();
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像压缩失败");
            throw;
        }
    }

    /// <summary>
    /// 零拷贝图像数据提取
    /// </summary>
    public unsafe ImageData ExtractImageDataZeroCopy(Bitmap bitmap)
    {
        if (bitmap == null)
            throw new ArgumentNullException(nameof(bitmap));

        var bitmapData = bitmap.LockBits(
            new Rectangle(0, 0, bitmap.Width, bitmap.Height),
            ImageLockMode.ReadOnly,
            PixelFormat.Format32bppArgb);

        try
        {
            var imageData = new ImageData
            {
                Width = bitmap.Width,
                Height = bitmap.Height,
                Stride = bitmapData.Stride,
                DataPointer = bitmapData.Scan0,
                PixelFormat = bitmapData.PixelFormat
            };

            return imageData;
        }
        finally
        {
            bitmap.UnlockBits(bitmapData);
        }
    }

    /// <summary>
    /// 内存池优化的字节数组操作
    /// </summary>
    public async Task<T> ProcessWithPooledMemoryAsync<T>(int bufferSize, Func<byte[], Task<T>> processor)
    {
        var buffer = _bytePool.Rent(bufferSize);
        try
        {
            return await processor(buffer);
        }
        finally
        {
            _bytePool.Return(buffer, clearArray: true);
        }
    }

    /// <summary>
    /// 计算目标尺寸
    /// </summary>
    private (int width, int height) CalculateTargetSize(
        int originalWidth, int originalHeight,
        int targetWidth, int targetHeight,
        bool maintainAspectRatio)
    {
        if (!maintainAspectRatio)
            return (targetWidth, targetHeight);

        var aspectRatio = (double)originalWidth / originalHeight;
        
        if (targetWidth / aspectRatio <= targetHeight)
        {
            return (targetWidth, (int)(targetWidth / aspectRatio));
        }
        else
        {
            return ((int)(targetHeight * aspectRatio), targetHeight);
        }
    }

    /// <summary>
    /// 图像数据结构
    /// </summary>
    public struct ImageData
    {
        public int Width { get; set; }
        public int Height { get; set; }
        public int Stride { get; set; }
        public IntPtr DataPointer { get; set; }
        public PixelFormat PixelFormat { get; set; }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            GC.SuppressFinalize(this);
        }
    }
}
