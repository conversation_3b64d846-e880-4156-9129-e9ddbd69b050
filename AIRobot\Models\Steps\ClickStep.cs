using System.Diagnostics;
using System.Drawing;
using AIRobot.Core.Interfaces;

namespace AIRobot.Core.Models.Steps;

/// <summary>
/// 点击步骤
/// </summary>
public class ClickStep : AutomationStep
{
    public Point Position { get; set; }
    public MouseButton Button { get; set; } = MouseButton.Left;
    public bool IsDoubleClick { get; set; }
    
    public override async Task<StepResult> ExecuteAsync(IAutomationContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var inputSimulator = context.GetService<IInputSimulator>();
            
            context.Log(Microsoft.Extensions.Logging.LogLevel.Information, 
                $"执行点击操作: 位置({Position.X}, {Position.Y}), 按钮: {Button}, 双击: {IsDoubleClick}");
            
            if (IsDoubleClick)
            {
                await inputSimulator.DoubleClickAsync(Position);
            }
            else
            {
                await inputSimulator.ClickAsync(Position, Button);
            }
            
            if (Delay > TimeSpan.Zero)
            {
                await context.DelayAsync(Delay);
            }
            
            return new StepResult
            {
                IsSuccess = true,
                Message = $"成功点击位置 ({Position.X}, {Position.Y})",
                ExecutionTime = stopwatch.Elapsed
            };
        }
        catch (Exception ex)
        {
            context.Log(Microsoft.Extensions.Logging.LogLevel.Error, 
                $"点击操作失败: {ex.Message}");
            
            return new StepResult
            {
                IsSuccess = false,
                Message = $"点击失败: {ex.Message}",
                Exception = ex,
                ExecutionTime = stopwatch.Elapsed
            };
        }
    }
}
