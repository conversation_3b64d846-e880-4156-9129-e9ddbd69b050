using System.Drawing;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Core.Models.Steps;

namespace AIRobot.Tests.Core.Steps;

public class ClickStepTests
{
    private readonly Mock<IAutomationContext> _mockContext;
    private readonly Mock<IInputSimulator> _mockInputSimulator;

    public ClickStepTests()
    {
        _mockContext = new Mock<IAutomationContext>();
        _mockInputSimulator = new Mock<IInputSimulator>();
        
        _mockContext.Setup(x => x.GetService<IInputSimulator>())
            .Returns(_mockInputSimulator.Object);
    }

    [Fact]
    public async Task ExecuteAsync_WithValidPosition_ShouldReturnSuccess()
    {
        // Arrange
        var step = new ClickStep
        {
            Name = "测试点击",
            Position = new Point(100, 200),
            Button = MouseButton.Left
        };

        _mockInputSimulator.Setup(x => x.ClickAsync(It.IsAny<Point>(), It.IsAny<MouseButton>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await step.ExecuteAsync(_mockContext.Object);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Message.Should().Contain("成功点击");
        result.ExecutionTime.Should().BeGreaterThan(TimeSpan.Zero);

        _mockInputSimulator.Verify(x => x.ClickAsync(
            It.Is<Point>(p => p.X == 100 && p.Y == 200),
            MouseButton.Left), Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_WithDoubleClick_ShouldCallDoubleClick()
    {
        // Arrange
        var step = new ClickStep
        {
            Name = "测试双击",
            Position = new Point(150, 250),
            IsDoubleClick = true
        };

        _mockInputSimulator.Setup(x => x.DoubleClickAsync(It.IsAny<Point>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await step.ExecuteAsync(_mockContext.Object);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        _mockInputSimulator.Verify(x => x.DoubleClickAsync(
            It.Is<Point>(p => p.X == 150 && p.Y == 250)), Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_WithInputSimulatorException_ShouldReturnFailure()
    {
        // Arrange
        var step = new ClickStep
        {
            Name = "测试点击异常",
            Position = new Point(100, 200)
        };

        var expectedException = new Exception("输入模拟失败");
        _mockInputSimulator.Setup(x => x.ClickAsync(It.IsAny<Point>(), It.IsAny<MouseButton>()))
            .ThrowsAsync(expectedException);

        // Act
        var result = await step.ExecuteAsync(_mockContext.Object);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Message.Should().Contain("点击失败");
        result.Exception.Should().Be(expectedException);
    }

    [Fact]
    public async Task ExecuteAsync_WithDelay_ShouldWaitForDelay()
    {
        // Arrange
        var step = new ClickStep
        {
            Name = "测试延迟点击",
            Position = new Point(100, 200),
            Delay = TimeSpan.FromMilliseconds(100)
        };

        _mockInputSimulator.Setup(x => x.ClickAsync(It.IsAny<Point>(), It.IsAny<MouseButton>()))
            .Returns(Task.CompletedTask);

        _mockContext.Setup(x => x.DelayAsync(It.IsAny<TimeSpan>()))
            .Returns(Task.CompletedTask);

        // Act
        var startTime = DateTime.Now;
        var result = await step.ExecuteAsync(_mockContext.Object);
        var endTime = DateTime.Now;

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        
        _mockContext.Verify(x => x.DelayAsync(TimeSpan.FromMilliseconds(100)), Times.Once);
    }

    [Theory]
    [InlineData(MouseButton.Left)]
    [InlineData(MouseButton.Right)]
    [InlineData(MouseButton.Middle)]
    public async Task ExecuteAsync_WithDifferentMouseButtons_ShouldUseCorrectButton(MouseButton button)
    {
        // Arrange
        var step = new ClickStep
        {
            Name = $"测试{button}按钮点击",
            Position = new Point(100, 200),
            Button = button
        };

        _mockInputSimulator.Setup(x => x.ClickAsync(It.IsAny<Point>(), It.IsAny<MouseButton>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await step.ExecuteAsync(_mockContext.Object);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();

        _mockInputSimulator.Verify(x => x.ClickAsync(
            It.IsAny<Point>(), button), Times.Once);
    }
}
