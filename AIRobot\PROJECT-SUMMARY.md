# AIRobot 项目总结

## 📋 项目概述

AIRobot 是一个基于 .NET 9 Blazor MAUI 的跨平台自动化机器人程序，旨在实现跨 Windows、macOS 和 Android 平台的界面自动化操作。项目采用现代化的架构设计，支持屏幕捕获、OCR 文字识别、输入模拟等核心功能。够实现自动打开程序识别程序界面，找到要点击的按钮  模拟鼠标或者手指点击 键盘输入等等，实现ocr等等

## 🏗️ 架构特点

### 核心设计原则
- **跨平台兼容**: 使用抽象接口隔离平台差异
- **工厂模式**: 根据平台动态创建具体实现
- **模块化设计**: 功能模块独立，便于维护和扩展
- **依赖注入**: 降低耦合度，提高可测试性
- **异步编程**: 提升用户体验和性能

### 分层架构
```
┌─────────────────────────────────────┐
│     Presentation Layer (MAUI)      │  Blazor UI 界面
├─────────────────────────────────────┤
│     Application Layer (Core)       │  业务逻辑协调
├─────────────────────────────────────┤
│     Domain Layer (Core)            │  核心业务模型
├─────────────────────────────────────┤
│   Infrastructure Layer (Platforms) │  平台特定实现
└─────────────────────────────────────┘
```

## 🔧 技术栈

| 技术领域 | 选择的技术 | 版本 | 优势 |
|---------|-----------|------|------|
| 框架 | .NET 9 Blazor MAUI | 9.0.0 | 跨平台统一开发 |
| UI | Blazor Hybrid | - | 现代化组件开发 |
| 计算机视觉 | OpenCV 4.10 (OpenCvSharp4) | 4.10.0 | 成熟稳定，功能丰富 |
| OCR 主引擎 | PaddleOCR | 最新版 | 高精度中英文识别 |
| OCR 备用 | Tesseract.NET | 5.2.0 | 兼容性好，多语言 |
| 公式识别 | Surya OCR | 最新版 | 专业数学公式识别 |
| 图像处理 | ImageSharp + OpenCV | 3.1.5 | 高性能图像处理 |
| 智能分割 | Meta SAM 2 (可选) | 2.1 | 零样本精确分割 |
| 依赖注入 | Microsoft.Extensions.DI | 9.0.0 | 标准 DI 容器 |
| 日志 | Microsoft.Extensions.Logging | 9.0.0 | 结构化日志 |
| 测试 | xUnit + Moq + FluentAssertions | 最新版 | 完整测试生态 |

## 📁 项目结构

```
AIRobot/
├── src/
│   ├── AIRobot.Core/              # 核心业务逻辑
│   │   ├── Interfaces/            # 接口定义
│   │   ├── Models/                # 数据模型
│   │   ├── Services/              # 业务服务
│   │   └── Exceptions/            # 自定义异常
│   │
│   ├── AIRobot.Platforms/         # 平台特定实现
│   │   ├── Windows/               # Windows 实现
│   │   ├── MacOS/                 # macOS 实现 (待实现)
│   │   └── Android/               # Android 实现 (待实现)
│   │
│   ├── AIRobot.MAUI/             # MAUI 主项目
│   │   ├── Components/            # Blazor 组件
│   │   ├── Pages/                 # 页面
│   │   └── Services/              # UI 服务
│   │
│   └── AIRobot.Shared/           # 共享组件
│       ├── Components/            # 可重用组件
│       └── wwwroot/               # 静态资源
│
├── tests/                         # 测试项目
├── docs/                          # 文档
└── samples/                       # 示例代码
```

## 🚀 核心功能模块

### 1. 屏幕捕获模块 (IScreenCapture)
- 全屏截图
- 窗口截图
- 区域截图
- 窗口信息获取

### 2. 输入模拟模块 (IInputSimulator)
- 鼠标点击/双击/拖拽
- 键盘输入
- 组合键支持
- 精确坐标定位

### 3. 计算机视觉模块 (IComputerVisionService) ⭐ 新增
- **基础图像处理**: 使用 OpenCV 进行图像预处理、特征检测
- **模板匹配**: UI 元素识别和定位
- **智能分割**: 可选集成 Meta SAM 2 进行精确对象分割
- **轮廓检测**: 自动识别界面元素边界

### 4. OCR 识别模块 (IOcrService) ⭐ 增强
- **多引擎支持**: PaddleOCR (主) + Tesseract (备) + Surya (公式)
- **智能引擎选择**: 根据图像内容自动选择最佳 OCR 引擎
- **公式识别**: 专业数学公式识别，输出 LaTeX 格式
- **表格识别**: 支持表格结构识别和数据提取
- **详细位置信息**: 字符级别的精确位置数据
- **多语言支持**: 支持 80+ 种语言识别

### 5. 应用程序控制模块 (IApplicationController)
- 程序启动/关闭
- 窗口查找/管理
- 进程监控

### 6. 自动化任务引擎
- 任务步骤定义
- 执行上下文管理
- 错误处理和重试
- 变量存储和传递

## 🎯 Windows 平台实现重点

### Win32 API 集成
- **屏幕捕获**: BitBlt, GetDC, CreateCompatibleBitmap
- **输入模拟**: SendInput, SetCursorPos
- **窗口管理**: FindWindow, EnumWindows, GetWindowRect
- **进程控制**: CreateProcess, OpenProcess

### 权限处理
- UAC 权限检测
- 管理员权限提升
- 安全的 P/Invoke 调用

## 📚 文档体系

| 文档 | 描述 | 状态 |
|------|------|------|
| README.md | 项目概述和快速开始 | ✅ 完成 |
| Architecture.md | 详细架构设计 | ✅ 完成 |
| Windows-Implementation.md | Windows 平台实现 | ✅ 完成 |
| Development-Guide.md | 开发指南和规范 | ✅ 完成 |
| Code-Examples.md | 代码实现示例 | ✅ 完成 |
| Computer-Vision-Analysis.md | 计算机视觉技术分析 | ✅ 完成 |
| OCR-Implementation.md | OCR 多引擎实现方案 | ✅ 完成 |
| API-Reference.md | API 参考文档 | 🔄 待完成 |
| User-Guide.md | 用户使用指南 | 🔄 待完成 |

## 🛠️ 快速开始

### 环境要求
- .NET 9 SDK
- Visual Studio 2024 或 VS Code
- MAUI 工作负载

### 初始化项目
```bash
# Windows (PowerShell)
.\setup-project.ps1

# Linux/macOS (Bash)
./setup-project.sh
```

### 构建和运行
```bash
# 还原依赖
dotnet restore

# 构建项目
dotnet build

# 运行测试
dotnet test

# 运行 MAUI 应用
dotnet run --project src/AIRobot.MAUI
```

## 📈 开发计划

### 第一阶段 - 基础搭建 ✅
- [x] 项目架构设计
- [x] 文档编写
- [x] 项目结构创建
- [x] 初始化脚本

### 第二阶段 - Windows 核心功能 🔄
- [ ] 核心接口实现
- [ ] Windows 屏幕捕获
- [ ] Windows 输入模拟
- [ ] OCR 功能集成
- [ ] 基础 UI 界面

### 第三阶段 - 功能完善 📋
- [ ] 自动化任务引擎
- [ ] 错误处理和日志
- [ ] 性能优化
- [ ] 单元测试覆盖

### 第四阶段 - 多平台扩展 📋
- [ ] macOS 平台适配
- [ ] Android 平台适配
- [ ] 跨平台测试
- [ ] 发布和部署

## 🔍 关键技术决策

### 为什么选择 Blazor MAUI？
1. **统一技术栈**: 使用 C# 和 Blazor 开发跨平台应用
2. **原生性能**: MAUI 提供原生平台访问能力
3. **现代 UI**: Blazor 组件化开发，易于维护
4. **生态系统**: 丰富的 .NET 生态系统支持

### 为什么使用工厂模式？
1. **平台隔离**: 不同平台的实现完全独立
2. **运行时选择**: 根据当前平台动态创建实现
3. **易于扩展**: 新增平台只需实现接口
4. **测试友好**: 便于 Mock 和单元测试

### 为什么选择多引擎 OCR 方案？
1. **PaddleOCR 作为主引擎**:
   - 高精度中英文识别，准确率业界领先
   - 轻量级模型，推理速度快
   - 支持 80+ 种语言
   - 提供详细的字符级位置信息

2. **Tesseract 作为备用引擎**:
   - 成熟稳定，兼容性好
   - 支持 100+ 种语言
   - 开源免费，无许可证限制

3. **Surya OCR 用于公式识别**:
   - 专业的数学公式识别能力
   - 直接输出 LaTeX 格式
   - 支持复杂的数学表达式
   - 轻量级模型，适合集成

### 为什么选择 OpenCV？
1. **成熟稳定**: 20+ 年发展历史，工业级可靠性
2. **功能丰富**: 涵盖图像处理、特征检测、机器学习
3. **高性能**: C++ 核心，支持硬件加速
4. **跨平台**: 完美支持所有目标平台
5. **C# 绑定**: OpenCvSharp4 提供完整的 .NET 支持

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 发起 Discussion
- 提交 Pull Request

---

**注意**: 这是一个设计文档和初始化阶段的项目。实际代码实现将在后续阶段逐步完成。
