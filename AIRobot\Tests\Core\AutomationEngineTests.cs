using System.Drawing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Core.Models.Steps;
using AIRobot.Core.Services;

namespace AIRobot.Tests.Core;

public class AutomationEngineTests
{
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<ILogger<AutomationEngine>> _mockLogger;
    private readonly Mock<IInputSimulator> _mockInputSimulator;
    private readonly Mock<IScreenCapture> _mockScreenCapture;
    private readonly Mock<IOcrService> _mockOcrService;
    private readonly AutomationEngine _automationEngine;

    public AutomationEngineTests()
    {
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockLogger = new Mock<ILogger<AutomationEngine>>();
        _mockInputSimulator = new Mock<IInputSimulator>();
        _mockScreenCapture = new Mock<IScreenCapture>();
        _mockOcrService = new Mock<IOcrService>();

        _automationEngine = new AutomationEngine(_mockServiceProvider.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task ExecuteTaskAsync_WithValidTask_ShouldReturnSuccess()
    {
        // Arrange
        var task = new AutomationTask
        {
            Id = "test-task",
            Name = "测试任务",
            Steps = new List<AutomationStep>
            {
                new TestStep { Name = "步骤1", Order = 1 },
                new TestStep { Name = "步骤2", Order = 2 }
            }
        };

        SetupMockServices();

        // Act
        var result = await _automationEngine.ExecuteTaskAsync(task);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.StepResults.Should().HaveCount(2);
        result.TotalExecutionTime.Should().BeGreaterThan(TimeSpan.Zero);
    }

    [Fact]
    public async Task ExecuteTaskAsync_WithFailingStep_ShouldReturnFailure()
    {
        // Arrange
        var task = new AutomationTask
        {
            Id = "test-task",
            Name = "测试任务",
            Steps = new List<AutomationStep>
            {
                new TestStep { Name = "成功步骤", Order = 1 },
                new FailingTestStep { Name = "失败步骤", Order = 2 }
            }
        };

        SetupMockServices();

        // Act
        var result = await _automationEngine.ExecuteTaskAsync(task);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.StepResults.Should().HaveCount(2);
        result.StepResults.First().IsSuccess.Should().BeTrue();
        result.StepResults.Last().IsSuccess.Should().BeFalse();
    }

    [Fact]
    public async Task ExecuteTaskAsync_WithCancellation_ShouldReturnCancelled()
    {
        // Arrange
        var task = new AutomationTask
        {
            Id = "test-task",
            Name = "测试任务",
            Steps = new List<AutomationStep>
            {
                new DelayTestStep { Name = "延迟步骤", Order = 1, DelayMs = 5000 }
            }
        };

        SetupMockServices();

        using var cts = new CancellationTokenSource();
        cts.CancelAfter(100); // 100ms 后取消

        // Act
        var result = await _automationEngine.ExecuteTaskAsync(task, cts.Token);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Message.Should().Contain("取消");
    }

    [Fact]
    public void GetTaskStatus_WithNewTask_ShouldReturnNotStarted()
    {
        // Arrange
        var taskId = "new-task";

        // Act
        var status = _automationEngine.GetTaskStatus(taskId);

        // Assert
        status.Should().Be(TaskExecutionStatus.NotStarted);
    }

    [Fact]
    public async Task CancelTaskAsync_ShouldSetStatusToCancelled()
    {
        // Arrange
        var taskId = "test-task";

        // Act
        await _automationEngine.CancelTaskAsync(taskId);
        var status = _automationEngine.GetTaskStatus(taskId);

        // Assert
        status.Should().Be(TaskExecutionStatus.Cancelled);
    }

    private void SetupMockServices()
    {
        var mockContext = new Mock<IAutomationContext>();
        mockContext.Setup(x => x.GetService<IInputSimulator>()).Returns(_mockInputSimulator.Object);
        mockContext.Setup(x => x.GetService<IScreenCapture>()).Returns(_mockScreenCapture.Object);
        mockContext.Setup(x => x.GetService<IOcrService>()).Returns(_mockOcrService.Object);

        _mockServiceProvider.Setup(x => x.GetRequiredService<ILogger<AutomationContext>>())
            .Returns(new Mock<ILogger<AutomationContext>>().Object);
    }

    // 测试用的步骤类
    private class TestStep : AutomationStep
    {
        public override async Task<StepResult> ExecuteAsync(IAutomationContext context)
        {
            await Task.Delay(10); // 模拟执行时间
            return new StepResult
            {
                IsSuccess = true,
                Message = $"步骤 {Name} 执行成功"
            };
        }
    }

    private class FailingTestStep : AutomationStep
    {
        public override async Task<StepResult> ExecuteAsync(IAutomationContext context)
        {
            await Task.Delay(10);
            return new StepResult
            {
                IsSuccess = false,
                Message = $"步骤 {Name} 执行失败",
                Exception = new Exception("测试异常")
            };
        }
    }

    private class DelayTestStep : AutomationStep
    {
        public int DelayMs { get; set; } = 1000;

        public override async Task<StepResult> ExecuteAsync(IAutomationContext context)
        {
            await Task.Delay(DelayMs);
            return new StepResult
            {
                IsSuccess = true,
                Message = $"延迟步骤 {Name} 执行成功"
            };
        }
    }
}
