using System.Drawing;
using AIRobot.Core.Models;

namespace AIRobot.Core.Interfaces;

/// <summary>
/// 输入模拟接口
/// </summary>
public interface IInputSimulator
{
    /// <summary>
    /// 鼠标点击
    /// </summary>
    Task ClickAsync(Point position, MouseButton button = MouseButton.Left);
    
    /// <summary>
    /// 鼠标双击
    /// </summary>
    Task DoubleClickAsync(Point position);
    
    /// <summary>
    /// 鼠标拖拽
    /// </summary>
    Task DragAsync(Point from, Point to);
    
    /// <summary>
    /// 发送文本
    /// </summary>
    Task SendKeysAsync(string text);
    
    /// <summary>
    /// 发送按键
    /// </summary>
    Task SendKeyAsync(VirtualKey key);
    
    /// <summary>
    /// 组合键
    /// </summary>
    Task SendKeyComboAsync(params VirtualKey[] keys);
}
