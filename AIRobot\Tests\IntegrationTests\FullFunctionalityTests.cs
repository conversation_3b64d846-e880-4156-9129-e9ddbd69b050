using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using FluentAssertions;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Services;
using AIRobot.Core.Models;
using AIRobot.Core.Extensions;
using System.Drawing;

namespace AIRobot.Tests.IntegrationTests;

/// <summary>
/// 完整功能集成测试
/// 测试所有核心功能的端到端工作流程
/// </summary>
public class FullFunctionalityTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly ILogger<FullFunctionalityTests> _logger;

    public FullFunctionalityTests()
    {
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        services.AddAIRobotCore();
        services.AddPlatformServices();
        
        _serviceProvider = services.BuildServiceProvider();
        _logger = _serviceProvider.GetRequiredService<ILogger<FullFunctionalityTests>>();
    }

    [Fact]
    public async Task Test_Complete_OCR_Workflow()
    {
        // 安排
        _logger.LogInformation("开始完整 OCR 工作流程测试");
        
        var factory = _serviceProvider.GetRequiredService<IPlatformServiceFactory>();
        var screenCapture = factory.CreateScreenCapture();
        var ocrService = factory.CreateOcrService();

        // 执行
        var screenshot = await screenCapture.CaptureScreenAsync();
        screenshot.Should().NotBeNull().And.NotBeEmpty();

        var ocrResult = await ocrService.RecognizeTextAsync(screenshot);
        
        // 验证
        ocrResult.Should().NotBeNull();
        ocrResult.Text.Should().NotBeNull();
        ocrResult.Confidence.Should().BeGreaterOrEqualTo(0).And.BeLessOrEqualTo(1);
        ocrResult.ProcessingTime.Should().BePositive();
        ocrResult.UsedEngine.Should().NotBeNullOrEmpty();
        
        _logger.LogInformation("OCR 工作流程测试完成: 识别到 {TextLength} 个字符，置信度 {Confidence:P}", 
            ocrResult.Text.Length, ocrResult.Confidence);
    }

    [Fact]
    public async Task Test_Complete_Automation_Workflow()
    {
        // 安排
        _logger.LogInformation("开始完整自动化工作流程测试");
        
        var factory = _serviceProvider.GetRequiredService<IPlatformServiceFactory>();
        var automationEngine = factory.CreateAutomationEngine();
        
        var task = new AutomationTask
        {
            Name = "测试任务",
            Description = "集成测试自动化任务",
            Steps = new List<AutomationStep>
            {
                new ClickStep 
                { 
                    Name = "点击测试", 
                    Position = new Point(100, 100), 
                    Order = 1 
                },
                new DelayStep 
                { 
                    Name = "延迟测试", 
                    DelayMilliseconds = 500, 
                    Order = 2 
                },
                new TypeTextStep 
                { 
                    Name = "输入测试", 
                    Text = "Hello AIRobot!", 
                    Order = 3 
                }
            }
        };

        // 执行
        var result = await automationEngine.ExecuteTaskAsync(task);
        
        // 验证
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.StepResults.Should().HaveCount(3);
        result.TotalExecutionTime.Should().BePositive();
        
        foreach (var stepResult in result.StepResults)
        {
            stepResult.IsSuccess.Should().BeTrue();
            stepResult.ExecutionTime.Should().BePositive();
        }
        
        _logger.LogInformation("自动化工作流程测试完成: 执行了 {StepCount} 个步骤，总耗时 {TotalTime}ms", 
            result.StepResults.Count(), result.TotalExecutionTime.TotalMilliseconds);
    }

    [Fact]
    public async Task Test_Cross_Platform_Service_Factory()
    {
        // 安排
        _logger.LogInformation("开始跨平台服务工厂测试");
        
        var factory = _serviceProvider.GetRequiredService<IPlatformServiceFactory>();

        // 执行和验证
        factory.Should().NotBeNull();
        factory.IsPlatformSupported().Should().BeTrue();
        factory.GetPlatformName().Should().NotBeNullOrEmpty();

        // 测试所有服务创建
        var screenCapture = factory.CreateScreenCapture();
        screenCapture.Should().NotBeNull();

        var inputSimulator = factory.CreateInputSimulator();
        inputSimulator.Should().NotBeNull();

        var appController = factory.CreateApplicationController();
        appController.Should().NotBeNull();

        var ocrService = factory.CreateOcrService();
        ocrService.Should().NotBeNull();

        var visionService = factory.CreateComputerVisionService();
        visionService.Should().NotBeNull();

        var automationEngine = factory.CreateAutomationEngine();
        automationEngine.Should().NotBeNull();
        
        _logger.LogInformation("跨平台服务工厂测试完成: 平台 {Platform}", factory.GetPlatformName());
    }

    [Fact]
    public async Task Test_Screen_Capture_All_Methods()
    {
        // 安排
        _logger.LogInformation("开始屏幕捕获全方法测试");
        
        var factory = _serviceProvider.GetRequiredService<IPlatformServiceFactory>();
        var screenCapture = factory.CreateScreenCapture();

        // 测试全屏捕获
        var fullScreen = await screenCapture.CaptureScreenAsync();
        fullScreen.Should().NotBeNull().And.NotBeEmpty();

        // 测试窗口枚举
        var windows = await screenCapture.GetVisibleWindowsAsync();
        windows.Should().NotBeNull();
        
        // 如果有可见窗口，测试窗口捕获
        var firstWindow = windows.FirstOrDefault();
        if (firstWindow != null)
        {
            var windowCapture = await screenCapture.CaptureWindowAsync(firstWindow.Handle);
            windowCapture.Should().NotBeNull().And.NotBeEmpty();
        }

        // 测试区域捕获
        var region = new Rectangle(0, 0, 200, 200);
        var regionCapture = await screenCapture.CaptureRegionAsync(region);
        regionCapture.Should().NotBeNull().And.NotBeEmpty();
        
        _logger.LogInformation("屏幕捕获全方法测试完成: 找到 {WindowCount} 个窗口", windows.Count());
    }

    [Fact]
    public async Task Test_OCR_Multiple_Engines()
    {
        // 安排
        _logger.LogInformation("开始 OCR 多引擎测试");
        
        var factory = _serviceProvider.GetRequiredService<IPlatformServiceFactory>();
        var ocrService = factory.CreateOcrService();
        var screenCapture = factory.CreateScreenCapture();

        // 获取测试图像
        var testImage = await screenCapture.CaptureScreenAsync();

        // 测试不同引擎
        var engines = new[] { OcrEngine.Auto, OcrEngine.Tesseract };
        
        foreach (var engine in engines)
        {
            try
            {
                var result = await ocrService.RecognizeTextAsync(testImage, engine);
                result.Should().NotBeNull();
                result.UsedEngine.Should().NotBeNullOrEmpty();
                
                _logger.LogInformation("引擎 {Engine} 测试完成: 识别到 {TextLength} 个字符", 
                    engine, result.Text.Length);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("引擎 {Engine} 测试失败: {Error}", engine, ex.Message);
            }
        }
    }

    [Fact]
    public async Task Test_Computer_Vision_Features()
    {
        // 安排
        _logger.LogInformation("开始计算机视觉功能测试");
        
        var factory = _serviceProvider.GetRequiredService<IPlatformServiceFactory>();
        var visionService = factory.CreateComputerVisionService();
        var screenCapture = factory.CreateScreenCapture();

        // 获取测试图像
        var testImage = await screenCapture.CaptureScreenAsync();

        // 测试图像预处理
        var processedImage = await visionService.PreprocessImageAsync(testImage);
        processedImage.Should().NotBeNull().And.NotBeEmpty();

        // 测试轮廓检测
        var contours = await visionService.DetectContoursAsync(testImage);
        contours.Should().NotBeNull();

        // 测试自动分割
        var segments = await visionService.AutoSegmentAsync(testImage);
        segments.Should().NotBeNull();
        
        _logger.LogInformation("计算机视觉功能测试完成: 检测到 {ContourCount} 个轮廓，{SegmentCount} 个分割区域", 
            contours.Count(), segments.Count());
    }

    [Fact]
    public async Task Test_Permission_Manager()
    {
        // 安排
        _logger.LogInformation("开始权限管理器测试");
        
        var permissionManager = _serviceProvider.GetRequiredService<PermissionManager>();

        // 执行
        var status = await permissionManager.CheckAllPermissionsAsync();
        
        // 验证
        status.Should().NotBeNull();
        status.IsSupported.Should().BeTrue();
        status.OverallStatus.Should().NotBeNullOrEmpty();
        
        _logger.LogInformation("权限管理器测试完成: {Status}", status.OverallStatus);
    }

    [Fact]
    public async Task Test_Performance_Benchmarks()
    {
        // 安排
        _logger.LogInformation("开始性能基准测试");
        
        var factory = _serviceProvider.GetRequiredService<IPlatformServiceFactory>();
        var screenCapture = factory.CreateScreenCapture();
        var ocrService = factory.CreateOcrService();

        const int iterations = 5;
        var captureTimes = new List<TimeSpan>();
        var ocrTimes = new List<TimeSpan>();

        // 执行多次测试
        for (int i = 0; i < iterations; i++)
        {
            // 屏幕捕获性能测试
            var captureStart = DateTime.Now;
            var screenshot = await screenCapture.CaptureScreenAsync();
            var captureTime = DateTime.Now - captureStart;
            captureTimes.Add(captureTime);

            // OCR 性能测试
            var ocrStart = DateTime.Now;
            var ocrResult = await ocrService.RecognizeTextAsync(screenshot);
            var ocrTime = DateTime.Now - ocrStart;
            ocrTimes.Add(ocrTime);
        }

        // 验证性能指标
        var avgCaptureTime = captureTimes.Average(t => t.TotalMilliseconds);
        var avgOcrTime = ocrTimes.Average(t => t.TotalMilliseconds);

        avgCaptureTime.Should().BeLessThan(5000); // 屏幕捕获应在 5 秒内完成
        avgOcrTime.Should().BeLessThan(30000); // OCR 应在 30 秒内完成
        
        _logger.LogInformation("性能基准测试完成: 平均捕获时间 {CaptureTime}ms，平均 OCR 时间 {OcrTime}ms", 
            avgCaptureTime, avgOcrTime);
    }

    [Fact]
    public async Task Test_Error_Handling_And_Recovery()
    {
        // 安排
        _logger.LogInformation("开始错误处理和恢复测试");
        
        var factory = _serviceProvider.GetRequiredService<IPlatformServiceFactory>();
        var automationEngine = factory.CreateAutomationEngine();

        // 创建包含错误步骤的任务
        var task = new AutomationTask
        {
            Name = "错误测试任务",
            Description = "测试错误处理",
            Steps = new List<AutomationStep>
            {
                new ClickStep 
                { 
                    Name = "正常点击", 
                    Position = new Point(100, 100), 
                    Order = 1 
                },
                new ClickStep 
                { 
                    Name = "无效点击", 
                    Position = new Point(-1, -1), // 无效坐标
                    Order = 2 
                },
                new TypeTextStep 
                { 
                    Name = "正常输入", 
                    Text = "Recovery Test", 
                    Order = 3 
                }
            }
        };

        // 执行
        var result = await automationEngine.ExecuteTaskAsync(task);
        
        // 验证错误处理
        result.Should().NotBeNull();
        result.StepResults.Should().HaveCount(3);
        
        // 第一个步骤应该成功
        result.StepResults.First().IsSuccess.Should().BeTrue();
        
        // 应该有详细的错误信息
        if (!result.IsSuccess)
        {
            result.Message.Should().NotBeNullOrEmpty();
        }
        
        _logger.LogInformation("错误处理和恢复测试完成: 任务状态 {Success}", result.IsSuccess);
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}
