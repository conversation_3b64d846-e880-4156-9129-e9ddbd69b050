# AIRobot - 跨平台自动化机器人

## 项目概述

AIRobot 是一个基于 .NET 9 Blazor MAUI 的跨平台自动化机器人程序，支持 Windows、macOS 和 Android 平台。该程序能够实现界面识别、自动点击、键盘输入、OCR 文字识别等自动化功能。

## 支持平台

- ✅ Windows 11 (优先实现)
- 🔄 macOS (后续支持)
- 🔄 Android (后续支持)

## 核心功能

1. **界面识别与分析**
   - 屏幕截图捕获
   - UI 元素识别
   - 坐标定位

2. **自动化操作**
   - 鼠标点击模拟
   - 键盘输入模拟
   - 拖拽操作

3. **OCR 文字识别**
   - 图像文字提取
   - 多语言支持
   - 文字位置定位

4. **程序控制**
   - 应用程序启动
   - 窗口管理
   - 进程控制

## 技术栈

- **框架**: .NET 9 Blazor MAUI
- **UI**: Blazor Hybrid
- **计算机视觉**: OpenCV 4.10 (OpenCvSharp4)
- **OCR**: PaddleOCR (主要) + Tesseract (备用) + <PERSON><PERSON> (公式)
- **图像处理**: ImageSharp + OpenCV
- **自动化**: 平台特定 API
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **日志**: Microsoft.Extensions.Logging

## 项目结构

```
AIRobot/
├── src/
│   ├── AIRobot.Core/              # 核心业务逻辑
│   ├── AIRobot.Platforms/         # 平台特定实现
│   ├── AIRobot.MAUI/             # MAUI 主项目
│   └── AIRobot.Shared/           # 共享组件
├── tests/
│   ├── AIRobot.Core.Tests/
│   └── AIRobot.Platforms.Tests/
├── docs/                         # 文档
└── samples/                      # 示例代码
```

## 快速开始

1. 确保安装 .NET 9 SDK
2. 安装 MAUI 工作负载
3. 克隆项目并还原依赖
4. 运行项目

## 开发计划

- [x] 项目架构设计
- [ ] 核心接口定义
- [ ] Windows 平台实现
- [ ] OCR 功能集成
- [ ] UI 界面开发
- [ ] 测试用例编写
- [ ] macOS 平台适配
- [ ] Android 平台适配

## 贡献指南

请参考 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。
