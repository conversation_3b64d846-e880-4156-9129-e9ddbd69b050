# Windows 平台实现设计

## 1. Windows API 集成

### 1.1 所需 Windows API

#### 屏幕捕获相关
- `GetDC` / `ReleaseDC` - 获取设备上下文
- `CreateCompatibleDC` / `DeleteDC` - 创建兼容设备上下文
- `CreateCompatibleBitmap` / `DeleteObject` - 创建位图
- `BitBlt` - 位图传输
- `GetDIBits` - 获取位图数据

#### 窗口管理相关
- `FindWindow` / `FindWindowEx` - 查找窗口
- `GetWindowRect` / `GetClientRect` - 获取窗口矩形
- `EnumWindows` - 枚举窗口
- `GetWindowText` - 获取窗口标题
- `IsWindowVisible` - 检查窗口可见性
- `SetForegroundWindow` - 设置前台窗口

#### 输入模拟相关
- `SendInput` - 发送输入事件
- `SetCursorPos` - 设置鼠标位置
- `GetCursorPos` - 获取鼠标位置
- `mouse_event` - 鼠标事件 (已弃用，使用 SendInput)
- `keybd_event` - 键盘事件 (已弃用，使用 SendInput)

#### 进程管理相关
- `CreateProcess` - 创建进程
- `OpenProcess` - 打开进程
- `TerminateProcess` - 终止进程
- `GetProcessImageFileName` - 获取进程路径

### 1.2 P/Invoke 声明示例

```csharp
public static class Win32Api
{
    [DllImport("user32.dll")]
    public static extern IntPtr GetDC(IntPtr hWnd);

    [DllImport("user32.dll")]
    public static extern int ReleaseDC(IntPtr hWnd, IntPtr hDC);

    [DllImport("gdi32.dll")]
    public static extern IntPtr CreateCompatibleDC(IntPtr hDC);

    [DllImport("gdi32.dll")]
    public static extern IntPtr CreateCompatibleBitmap(IntPtr hDC, int nWidth, int nHeight);

    [DllImport("gdi32.dll")]
    public static extern IntPtr SelectObject(IntPtr hDC, IntPtr hObject);

    [DllImport("gdi32.dll")]
    public static extern bool BitBlt(IntPtr hDestDC, int x, int y, int nWidth, int nHeight,
        IntPtr hSrcDC, int xSrc, int ySrc, uint dwRop);

    [DllImport("user32.dll")]
    public static extern bool SendInput(uint nInputs, INPUT[] pInputs, int cbSize);

    [DllImport("user32.dll")]
    public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

    [DllImport("user32.dll")]
    public static extern bool EnumWindows(EnumWindowsProc lpEnumFunc, IntPtr lParam);

    [DllImport("user32.dll")]
    public static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

    [DllImport("user32.dll")]
    public static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);
}
```

## 2. 核心实现类

### 2.1 WindowsScreenCapture

```csharp
public class WindowsScreenCapture : IScreenCapture
{
    public async Task<byte[]> CaptureScreenAsync()
    {
        return await Task.Run(() =>
        {
            var screenBounds = Screen.PrimaryScreen.Bounds;
            using var bitmap = new Bitmap(screenBounds.Width, screenBounds.Height);
            using var graphics = Graphics.FromImage(bitmap);
            
            graphics.CopyFromScreen(0, 0, 0, 0, screenBounds.Size);
            
            using var stream = new MemoryStream();
            bitmap.Save(stream, ImageFormat.Png);
            return stream.ToArray();
        });
    }

    public async Task<byte[]> CaptureWindowAsync(IntPtr windowHandle)
    {
        return await Task.Run(() =>
        {
            Win32Api.GetWindowRect(windowHandle, out var rect);
            var width = rect.Right - rect.Left;
            var height = rect.Bottom - rect.Top;

            using var bitmap = new Bitmap(width, height);
            using var graphics = Graphics.FromImage(bitmap);
            
            var hdcSrc = Win32Api.GetDC(windowHandle);
            var hdcDest = graphics.GetHdc();
            
            Win32Api.BitBlt(hdcDest, 0, 0, width, height, hdcSrc, 0, 0, 0x00CC0020);
            
            graphics.ReleaseHdc(hdcDest);
            Win32Api.ReleaseDC(windowHandle, hdcSrc);
            
            using var stream = new MemoryStream();
            bitmap.Save(stream, ImageFormat.Png);
            return stream.ToArray();
        });
    }
}
```

### 2.2 WindowsInputSimulator

```csharp
public class WindowsInputSimulator : IInputSimulator
{
    public async Task ClickAsync(Point position, MouseButton button = MouseButton.Left)
    {
        await Task.Run(() =>
        {
            Win32Api.SetCursorPos(position.X, position.Y);
            
            var inputs = new INPUT[2];
            inputs[0] = CreateMouseInput(GetMouseDownFlag(button));
            inputs[1] = CreateMouseInput(GetMouseUpFlag(button));
            
            Win32Api.SendInput((uint)inputs.Length, inputs, Marshal.SizeOf<INPUT>());
        });
    }

    public async Task SendKeysAsync(string text)
    {
        await Task.Run(() =>
        {
            var inputs = new List<INPUT>();
            
            foreach (char c in text)
            {
                var vk = VkKeyScan(c);
                inputs.Add(CreateKeyboardInput((ushort)(vk & 0xFF), false));
                inputs.Add(CreateKeyboardInput((ushort)(vk & 0xFF), true));
            }
            
            Win32Api.SendInput((uint)inputs.Count, inputs.ToArray(), Marshal.SizeOf<INPUT>());
        });
    }

    private INPUT CreateMouseInput(uint flags)
    {
        return new INPUT
        {
            type = 0, // INPUT_MOUSE
            u = new InputUnion
            {
                mi = new MOUSEINPUT
                {
                    dwFlags = flags,
                    time = 0,
                    dwExtraInfo = IntPtr.Zero
                }
            }
        };
    }

    private INPUT CreateKeyboardInput(ushort vk, bool keyUp)
    {
        return new INPUT
        {
            type = 1, // INPUT_KEYBOARD
            u = new InputUnion
            {
                ki = new KEYBDINPUT
                {
                    wVk = vk,
                    wScan = 0,
                    dwFlags = keyUp ? 0x0002u : 0, // KEYEVENTF_KEYUP
                    time = 0,
                    dwExtraInfo = IntPtr.Zero
                }
            }
        };
    }
}
```

### 2.3 WindowsApplicationController

```csharp
public class WindowsApplicationController : IApplicationController
{
    public async Task<Process> LaunchApplicationAsync(string applicationPath)
    {
        return await Task.Run(() =>
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = applicationPath,
                UseShellExecute = true
            };
            
            return Process.Start(startInfo);
        });
    }

    public async Task<bool> IsApplicationRunningAsync(string processName)
    {
        return await Task.Run(() =>
        {
            var processes = Process.GetProcessesByName(processName);
            return processes.Length > 0;
        });
    }

    public async Task<IntPtr> FindWindowAsync(string windowTitle)
    {
        return await Task.Run(() =>
        {
            return Win32Api.FindWindow(null, windowTitle);
        });
    }

    public async Task<IEnumerable<WindowInfo>> GetVisibleWindowsAsync()
    {
        return await Task.Run(() =>
        {
            var windows = new List<WindowInfo>();
            
            Win32Api.EnumWindows((hWnd, lParam) =>
            {
                if (Win32Api.IsWindowVisible(hWnd))
                {
                    var title = GetWindowTitle(hWnd);
                    if (!string.IsNullOrEmpty(title))
                    {
                        Win32Api.GetWindowRect(hWnd, out var rect);
                        
                        windows.Add(new WindowInfo
                        {
                            Handle = hWnd,
                            Title = title,
                            Bounds = new Rectangle(rect.Left, rect.Top, 
                                rect.Right - rect.Left, rect.Bottom - rect.Top),
                            IsVisible = true
                        });
                    }
                }
                return true;
            }, IntPtr.Zero);
            
            return windows;
        });
    }

    private string GetWindowTitle(IntPtr hWnd)
    {
        var title = new StringBuilder(256);
        Win32Api.GetWindowText(hWnd, title, title.Capacity);
        return title.ToString();
    }
}
```

## 3. 数据结构定义

### 3.1 Win32 结构体

```csharp
[StructLayout(LayoutKind.Sequential)]
public struct RECT
{
    public int Left;
    public int Top;
    public int Right;
    public int Bottom;
}

[StructLayout(LayoutKind.Sequential)]
public struct INPUT
{
    public uint type;
    public InputUnion u;
}

[StructLayout(LayoutKind.Explicit)]
public struct InputUnion
{
    [FieldOffset(0)]
    public MOUSEINPUT mi;
    [FieldOffset(0)]
    public KEYBDINPUT ki;
}

[StructLayout(LayoutKind.Sequential)]
public struct MOUSEINPUT
{
    public int dx;
    public int dy;
    public uint mouseData;
    public uint dwFlags;
    public uint time;
    public IntPtr dwExtraInfo;
}

[StructLayout(LayoutKind.Sequential)]
public struct KEYBDINPUT
{
    public ushort wVk;
    public ushort wScan;
    public uint dwFlags;
    public uint time;
    public IntPtr dwExtraInfo;
}
```

## 4. 权限和安全

### 4.1 所需权限

- **屏幕捕获**: 无需特殊权限
- **输入模拟**: 可能需要管理员权限
- **进程控制**: 根据目标进程权限而定

### 4.2 UAC 处理

```csharp
public static class UacHelper
{
    public static bool IsRunningAsAdmin()
    {
        var identity = WindowsIdentity.GetCurrent();
        var principal = new WindowsPrincipal(identity);
        return principal.IsInRole(WindowsBuiltInRole.Administrator);
    }

    public static void RestartAsAdmin()
    {
        var startInfo = new ProcessStartInfo
        {
            UseShellExecute = true,
            WorkingDirectory = Environment.CurrentDirectory,
            FileName = Assembly.GetExecutingAssembly().Location,
            Verb = "runas"
        };
        
        Process.Start(startInfo);
        Environment.Exit(0);
    }
}
```

## 5. 错误处理

### 5.1 常见错误

- **权限不足**: 提示用户以管理员身份运行
- **窗口未找到**: 检查应用程序是否运行
- **API 调用失败**: 记录详细错误信息

### 5.2 重试机制

```csharp
public static async Task<T> RetryAsync<T>(Func<Task<T>> operation, int maxRetries = 3)
{
    for (int i = 0; i < maxRetries; i++)
    {
        try
        {
            return await operation();
        }
        catch (Exception ex) when (i < maxRetries - 1)
        {
            await Task.Delay(1000 * (i + 1)); // 递增延迟
        }
    }
    
    return await operation(); // 最后一次尝试，不捕获异常
}
```
