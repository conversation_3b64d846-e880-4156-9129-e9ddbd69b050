# AIRobot 代码实现示例

## 1. 核心接口定义

### 1.1 IScreenCapture 接口

```csharp
// src/AIRobot.Core/Interfaces/IScreenCapture.cs
using System.Drawing;

namespace AIRobot.Core.Interfaces;

public interface IScreenCapture
{
    /// <summary>
    /// 捕获整个屏幕
    /// </summary>
    Task<byte[]> CaptureScreenAsync();
    
    /// <summary>
    /// 捕获指定窗口
    /// </summary>
    Task<byte[]> CaptureWindowAsync(IntPtr windowHandle);
    
    /// <summary>
    /// 捕获屏幕区域
    /// </summary>
    Task<byte[]> CaptureRegionAsync(Rectangle region);
    
    /// <summary>
    /// 获取窗口边界
    /// </summary>
    Task<Rectangle> GetWindowBoundsAsync(IntPtr windowHandle);
    
    /// <summary>
    /// 获取所有可见窗口
    /// </summary>
    Task<IEnumerable<WindowInfo>> GetVisibleWindowsAsync();
}
```

### 1.2 IInputSimulator 接口

```csharp
// src/AIRobot.Core/Interfaces/IInputSimulator.cs
using System.Drawing;

namespace AIRobot.Core.Interfaces;

public interface IInputSimulator
{
    /// <summary>
    /// 鼠标点击
    /// </summary>
    Task ClickAsync(Point position, MouseButton button = MouseButton.Left);
    
    /// <summary>
    /// 鼠标双击
    /// </summary>
    Task DoubleClickAsync(Point position);
    
    /// <summary>
    /// 鼠标拖拽
    /// </summary>
    Task DragAsync(Point from, Point to);
    
    /// <summary>
    /// 发送文本
    /// </summary>
    Task SendKeysAsync(string text);
    
    /// <summary>
    /// 发送按键
    /// </summary>
    Task SendKeyAsync(VirtualKey key);
    
    /// <summary>
    /// 组合键
    /// </summary>
    Task SendKeyComboAsync(params VirtualKey[] keys);
}
```

### 1.3 IOcrService 接口

```csharp
// src/AIRobot.Core/Interfaces/IOcrService.cs
namespace AIRobot.Core.Interfaces;

public interface IOcrService
{
    /// <summary>
    /// 识别图像中的文字
    /// </summary>
    Task<OcrResult> RecognizeTextAsync(byte[] imageData);
    
    /// <summary>
    /// 查找指定文字的位置
    /// </summary>
    Task<IEnumerable<TextRegion>> FindTextAsync(byte[] imageData, string searchText);
    
    /// <summary>
    /// 检查图像是否包含指定文字
    /// </summary>
    Task<bool> ContainsTextAsync(byte[] imageData, string searchText);
    
    /// <summary>
    /// 设置 OCR 语言
    /// </summary>
    Task SetLanguageAsync(string languageCode);
}
```

## 2. 数据模型

### 2.1 核心模型类

```csharp
// src/AIRobot.Core/Models/WindowInfo.cs
using System.Drawing;

namespace AIRobot.Core.Models;

public class WindowInfo
{
    public IntPtr Handle { get; set; }
    public string Title { get; set; } = string.Empty;
    public string ProcessName { get; set; } = string.Empty;
    public Rectangle Bounds { get; set; }
    public bool IsVisible { get; set; }
    public bool IsMinimized { get; set; }
    public int ProcessId { get; set; }
}

// src/AIRobot.Core/Models/OcrResult.cs
namespace AIRobot.Core.Models;

public class OcrResult
{
    public string Text { get; set; } = string.Empty;
    public float Confidence { get; set; }
    public IEnumerable<TextRegion> Regions { get; set; } = new List<TextRegion>();
    public TimeSpan ProcessingTime { get; set; }
}

// src/AIRobot.Core/Models/TextRegion.cs
using System.Drawing;

namespace AIRobot.Core.Models;

public class TextRegion
{
    public string Text { get; set; } = string.Empty;
    public Rectangle Bounds { get; set; }
    public float Confidence { get; set; }
    public int LineNumber { get; set; }
    public int WordIndex { get; set; }
}
```

### 2.2 自动化任务模型

```csharp
// src/AIRobot.Core/Models/AutomationTask.cs
namespace AIRobot.Core.Models;

public class AutomationTask
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public IList<AutomationStep> Steps { get; set; } = new List<AutomationStep>();
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public bool IsEnabled { get; set; } = true;
}

// src/AIRobot.Core/Models/AutomationStep.cs
namespace AIRobot.Core.Models;

public abstract class AutomationStep
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Order { get; set; }
    public bool IsEnabled { get; set; } = true;
    public TimeSpan Delay { get; set; } = TimeSpan.Zero;
    
    public abstract Task<StepResult> ExecuteAsync(IAutomationContext context);
}

// src/AIRobot.Core/Models/StepResult.cs
namespace AIRobot.Core.Models;

public class StepResult
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public Exception? Exception { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}
```

## 3. 具体步骤实现

### 3.1 点击步骤

```csharp
// src/AIRobot.Core/Models/Steps/ClickStep.cs
using System.Drawing;

namespace AIRobot.Core.Models.Steps;

public class ClickStep : AutomationStep
{
    public Point Position { get; set; }
    public MouseButton Button { get; set; } = MouseButton.Left;
    public bool IsDoubleClick { get; set; }
    
    public override async Task<StepResult> ExecuteAsync(IAutomationContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var inputSimulator = context.GetService<IInputSimulator>();
            
            if (IsDoubleClick)
            {
                await inputSimulator.DoubleClickAsync(Position);
            }
            else
            {
                await inputSimulator.ClickAsync(Position, Button);
            }
            
            if (Delay > TimeSpan.Zero)
            {
                await Task.Delay(Delay);
            }
            
            return new StepResult
            {
                IsSuccess = true,
                Message = $"成功点击位置 ({Position.X}, {Position.Y})",
                ExecutionTime = stopwatch.Elapsed
            };
        }
        catch (Exception ex)
        {
            return new StepResult
            {
                IsSuccess = false,
                Message = $"点击失败: {ex.Message}",
                Exception = ex,
                ExecutionTime = stopwatch.Elapsed
            };
        }
    }
}
```

### 3.2 文字输入步骤

```csharp
// src/AIRobot.Core/Models/Steps/TypeTextStep.cs
namespace AIRobot.Core.Models.Steps;

public class TypeTextStep : AutomationStep
{
    public string Text { get; set; } = string.Empty;
    public bool ClearBefore { get; set; }
    public TimeSpan TypingDelay { get; set; } = TimeSpan.FromMilliseconds(50);
    
    public override async Task<StepResult> ExecuteAsync(IAutomationContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var inputSimulator = context.GetService<IInputSimulator>();
            
            if (ClearBefore)
            {
                await inputSimulator.SendKeyComboAsync(VirtualKey.Control, VirtualKey.A);
                await Task.Delay(100);
            }
            
            await inputSimulator.SendKeysAsync(Text);
            
            if (Delay > TimeSpan.Zero)
            {
                await Task.Delay(Delay);
            }
            
            return new StepResult
            {
                IsSuccess = true,
                Message = $"成功输入文字: {Text}",
                ExecutionTime = stopwatch.Elapsed
            };
        }
        catch (Exception ex)
        {
            return new StepResult
            {
                IsSuccess = false,
                Message = $"文字输入失败: {ex.Message}",
                Exception = ex,
                ExecutionTime = stopwatch.Elapsed
            };
        }
    }
}
```

### 3.3 OCR 查找步骤

```csharp
// src/AIRobot.Core/Models/Steps/FindTextStep.cs
namespace AIRobot.Core.Models.Steps;

public class FindTextStep : AutomationStep
{
    public string SearchText { get; set; } = string.Empty;
    public Rectangle? SearchRegion { get; set; }
    public float MinConfidence { get; set; } = 0.8f;
    public bool ClickIfFound { get; set; }
    
    public override async Task<StepResult> ExecuteAsync(IAutomationContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var screenCapture = context.GetService<IScreenCapture>();
            var ocrService = context.GetService<IOcrService>();
            
            byte[] imageData;
            if (SearchRegion.HasValue)
            {
                imageData = await screenCapture.CaptureRegionAsync(SearchRegion.Value);
            }
            else
            {
                imageData = await screenCapture.CaptureScreenAsync();
            }
            
            var textRegions = await ocrService.FindTextAsync(imageData, SearchText);
            var validRegions = textRegions.Where(r => r.Confidence >= MinConfidence).ToList();
            
            if (validRegions.Any())
            {
                var result = new StepResult
                {
                    IsSuccess = true,
                    Message = $"找到文字 '{SearchText}', 共 {validRegions.Count} 个匹配",
                    ExecutionTime = stopwatch.Elapsed
                };
                
                result.Data["FoundRegions"] = validRegions;
                
                if (ClickIfFound)
                {
                    var firstRegion = validRegions.First();
                    var clickPoint = new Point(
                        firstRegion.Bounds.X + firstRegion.Bounds.Width / 2,
                        firstRegion.Bounds.Y + firstRegion.Bounds.Height / 2
                    );
                    
                    var inputSimulator = context.GetService<IInputSimulator>();
                    await inputSimulator.ClickAsync(clickPoint);
                    
                    result.Message += $", 已点击位置 ({clickPoint.X}, {clickPoint.Y})";
                }
                
                return result;
            }
            else
            {
                return new StepResult
                {
                    IsSuccess = false,
                    Message = $"未找到文字 '{SearchText}'",
                    ExecutionTime = stopwatch.Elapsed
                };
            }
        }
        catch (Exception ex)
        {
            return new StepResult
            {
                IsSuccess = false,
                Message = $"文字查找失败: {ex.Message}",
                Exception = ex,
                ExecutionTime = stopwatch.Elapsed
            };
        }
    }
}
```

## 4. 自动化上下文

```csharp
// src/AIRobot.Core/Interfaces/IAutomationContext.cs
namespace AIRobot.Core.Interfaces;

public interface IAutomationContext
{
    T GetService<T>() where T : class;
    void SetVariable(string name, object value);
    T? GetVariable<T>(string name);
    bool HasVariable(string name);
    void Log(LogLevel level, string message);
    Task DelayAsync(TimeSpan delay);
}

// src/AIRobot.Core/Services/AutomationContext.cs
namespace AIRobot.Core.Services;

public class AutomationContext : IAutomationContext
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<AutomationContext> _logger;
    private readonly Dictionary<string, object> _variables = new();

    public AutomationContext(IServiceProvider serviceProvider, ILogger<AutomationContext> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public T GetService<T>() where T : class
    {
        return _serviceProvider.GetRequiredService<T>();
    }

    public void SetVariable(string name, object value)
    {
        _variables[name] = value;
    }

    public T? GetVariable<T>(string name)
    {
        if (_variables.TryGetValue(name, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    public bool HasVariable(string name)
    {
        return _variables.ContainsKey(name);
    }

    public void Log(LogLevel level, string message)
    {
        _logger.Log(level, message);
    }

    public async Task DelayAsync(TimeSpan delay)
    {
        await Task.Delay(delay);
    }
}
```
