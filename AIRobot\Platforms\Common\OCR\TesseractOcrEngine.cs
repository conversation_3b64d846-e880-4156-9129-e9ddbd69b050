using System.Drawing;
using System.Diagnostics;
using Microsoft.Extensions.Logging;
using Tesseract;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Core.Exceptions;

namespace AIRobot.Platforms.Common.OCR;

/// <summary>
/// Tesseract OCR 引擎实现
/// </summary>
public class TesseractOcrEngine : IOcrEngine, IDisposable
{
    private readonly ILogger<TesseractOcrEngine> _logger;
    private readonly string _dataPath;
    private TesseractEngine? _engine;
    private readonly object _lock = new object();

    public OcrEngine EngineType => OcrEngine.Tesseract;

    public TesseractOcrEngine(ILogger<TesseractOcrEngine> logger, string dataPath = "./tessdata")
    {
        _logger = logger;
        _dataPath = dataPath;
        InitializeEngine();
    }

    private void InitializeEngine()
    {
        try
        {
            _logger.LogDebug("初始化 Tesseract 引擎，数据路径: {DataPath}", _dataPath);
            
            // 确保数据目录存在
            if (!Directory.Exists(_dataPath))
            {
                Directory.CreateDirectory(_dataPath);
                _logger.LogWarning("Tesseract 数据目录不存在，已创建: {DataPath}", _dataPath);
            }

            _engine = new TesseractEngine(_dataPath, "eng", EngineMode.Default);
            _logger.LogInformation("Tesseract 引擎初始化成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Tesseract 引擎初始化失败");
            throw new OcrException("Tesseract 引擎初始化失败", ex);
        }
    }

    public async Task<OcrResult> RecognizeTextAsync(byte[] imageData, string language = "eng")
    {
        return await Task.Run(() =>
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                lock (_lock)
                {
                    if (_engine == null)
                    {
                        throw new OcrException("Tesseract 引擎未初始化");
                    }

                    _logger.LogDebug("开始 Tesseract OCR 识别，语言: {Language}", language);

                    // 如果语言不同，重新初始化引擎
                    if (_engine.DefaultPageSegMode.ToString() != language)
                    {
                        _engine.Dispose();
                        _engine = new TesseractEngine(_dataPath, language, EngineMode.Default);
                    }

                    using var pix = Pix.LoadFromMemory(imageData);
                    using var page = _engine.Process(pix);

                    var text = page.GetText();
                    var confidence = page.GetMeanConfidence();

                    var regions = new List<TextRegion>();
                    
                    // 获取详细的识别结果
                    using var iterator = page.GetIterator();
                    iterator.Begin();

                    int lineNumber = 0;
                    int wordIndex = 0;

                    do
                    {
                        if (iterator.TryGetBoundingBox(PageIteratorLevel.Word, out var bounds))
                        {
                            var wordText = iterator.GetText(PageIteratorLevel.Word);
                            var wordConfidence = iterator.GetConfidence(PageIteratorLevel.Word);

                            if (!string.IsNullOrWhiteSpace(wordText))
                            {
                                regions.Add(new TextRegion
                                {
                                    Text = wordText.Trim(),
                                    Bounds = new Rectangle(bounds.X1, bounds.Y1, 
                                        bounds.X2 - bounds.X1, bounds.Y2 - bounds.Y1),
                                    Confidence = wordConfidence / 100f,
                                    LineNumber = lineNumber,
                                    WordIndex = wordIndex++
                                });
                            }
                        }

                        if (iterator.IsAtBeginningOf(PageIteratorLevel.TextLine))
                        {
                            lineNumber++;
                            wordIndex = 0;
                        }

                    } while (iterator.Next(PageIteratorLevel.Word));

                    var result = new OcrResult
                    {
                        Text = text?.Trim() ?? string.Empty,
                        Confidence = confidence / 100f,
                        Regions = regions,
                        ProcessingTime = stopwatch.Elapsed,
                        UsedEngine = OcrEngine.Tesseract,
                        Language = language
                    };

                    _logger.LogDebug("Tesseract OCR 识别完成，文本长度: {Length}, 置信度: {Confidence}, 耗时: {Duration}ms",
                        result.Text.Length, result.Confidence, result.ProcessingTime.TotalMilliseconds);

                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Tesseract OCR 识别失败");
                throw new OcrException("Tesseract OCR 识别失败", ex);
            }
        });
    }

    public async Task<IEnumerable<TextRegion>> FindTextAsync(byte[] imageData, string searchText)
    {
        var result = await RecognizeTextAsync(imageData);
        
        return result.Regions.Where(region => 
            region.Text.Contains(searchText, StringComparison.OrdinalIgnoreCase));
    }

    public async Task<bool> ContainsTextAsync(byte[] imageData, string searchText)
    {
        var regions = await FindTextAsync(imageData, searchText);
        return regions.Any();
    }

    public async Task SetLanguageAsync(string languageCode)
    {
        await Task.Run(() =>
        {
            try
            {
                lock (_lock)
                {
                    _logger.LogInformation("切换 Tesseract 语言到: {Language}", languageCode);
                    
                    _engine?.Dispose();
                    _engine = new TesseractEngine(_dataPath, languageCode, EngineMode.Default);
                    
                    _logger.LogDebug("Tesseract 语言切换完成");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Tesseract 语言切换失败: {Language}", languageCode);
                throw new OcrException($"语言切换失败: {languageCode}", ex);
            }
        });
    }

    public void Dispose()
    {
        lock (_lock)
        {
            _engine?.Dispose();
            _engine = null;
        }
        _logger.LogDebug("Tesseract 引擎已释放");
    }
}

/// <summary>
/// OCR 引擎接口
/// </summary>
public interface IOcrEngine
{
    OcrEngine EngineType { get; }
    Task<OcrResult> RecognizeTextAsync(byte[] imageData, string language = "auto");
    Task<IEnumerable<TextRegion>> FindTextAsync(byte[] imageData, string searchText);
    Task<bool> ContainsTextAsync(byte[] imageData, string searchText);
    Task SetLanguageAsync(string languageCode);
}
