using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Services;
using AIRobot.Platforms.Windows;
using AIRobot.Platforms.macOS;
using AIRobot.Platforms.Android;

namespace AIRobot.Core.Extensions;

/// <summary>
/// 服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 AIRobot 核心服务
    /// </summary>
    public static IServiceCollection AddAIRobotCore(this IServiceCollection services)
    {
        // 注册核心服务
        services.AddSingleton<IAutomationEngine, AutomationEngine>();
        services.AddTransient<IAutomationContext, AutomationContext>();
        services.AddTransient<SmartOcrService>();
        services.AddTransient<TesseractOcrEngine>();
        services.AddTransient<PaddleOcrEngine>();
        services.AddTransient<OpenCvComputerVisionService>();

        // 性能优化服务
        services.AddSingleton<OptimizedImageProcessor>();
        services.AddSingleton<SmartCacheService>();
        services.AddSingleton<PerformanceMonitor>();
        services.AddSingleton<PermissionManager>();

        return services;
    }

    /// <summary>
    /// 添加平台特定服务
    /// </summary>
    public static IServiceCollection AddPlatformServices(this IServiceCollection services)
    {
#if WINDOWS
        services.AddWindowsPlatformServices();
        services.AddSingleton<IPlatformServiceFactory, WindowsPlatformServiceFactory>();
#elif MACCATALYST || MACOS
        services.AddMacOsPlatformServices();
        services.AddSingleton<IPlatformServiceFactory, MacOsPlatformServiceFactory>();
#elif ANDROID
        services.AddAndroidPlatformServices();
        services.AddSingleton<IPlatformServiceFactory, AndroidPlatformServiceFactory>();
#else
        // 运行时检测平台
        if (OperatingSystem.IsWindows())
        {
            services.AddWindowsPlatformServices();
            services.AddSingleton<IPlatformServiceFactory, WindowsPlatformServiceFactory>();
        }
        else if (OperatingSystem.IsMacOS())
        {
            services.AddMacOsPlatformServices();
            services.AddSingleton<IPlatformServiceFactory, MacOsPlatformServiceFactory>();
        }
        else if (OperatingSystem.IsAndroid())
        {
            services.AddAndroidPlatformServices();
            services.AddSingleton<IPlatformServiceFactory, AndroidPlatformServiceFactory>();
        }
        else
        {
            // 默认使用 Windows 实现进行测试
            services.AddWindowsPlatformServices();
            services.AddSingleton<IPlatformServiceFactory, WindowsPlatformServiceFactory>();
        }
#endif

        // 注册平台服务的便捷访问
        services.AddTransient<IScreenCapture>(provider =>
            provider.GetRequiredService<IPlatformServiceFactory>().CreateScreenCapture());
        
        services.AddTransient<IInputSimulator>(provider =>
            provider.GetRequiredService<IPlatformServiceFactory>().CreateInputSimulator());
        
        services.AddTransient<IApplicationController>(provider =>
            provider.GetRequiredService<IPlatformServiceFactory>().CreateApplicationController());
        
        services.AddTransient<IComputerVisionService>(provider =>
            provider.GetRequiredService<IPlatformServiceFactory>().CreateComputerVisionService());
        
        services.AddTransient<IOcrService>(provider =>
            provider.GetRequiredService<IPlatformServiceFactory>().CreateOcrService());

        return services;
    }

    /// <summary>
    /// 添加日志服务
    /// </summary>
    public static IServiceCollection AddAIRobotLogging(this IServiceCollection services)
    {
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        return services;
    }

    /// <summary>
    /// 添加所有 AIRobot 服务
    /// </summary>
    public static IServiceCollection AddAIRobot(this IServiceCollection services)
    {
        return services
            .AddAIRobotLogging()
            .AddAIRobotCore()
            .AddPlatformServices();
    }
}
