using System.Drawing;
using Microsoft.Extensions.Logging;
using OpenCvSharp;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using AIRobot.Core.Exceptions;

namespace AIRobot.Platforms.Common;

/// <summary>
/// OpenCV 计算机视觉服务实现
/// </summary>
public class OpenCvComputerVisionService : IComputerVisionService
{
    private readonly ILogger<OpenCvComputerVisionService> _logger;

    public OpenCvComputerVisionService(ILogger<OpenCvComputerVisionService> logger)
    {
        _logger = logger;
    }

    public async Task<byte[]> PreprocessImageAsync(byte[] imageData, ImageProcessingOptions options)
    {
        return await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("开始图像预处理，选项: {Options}", options);

                using var mat = Mat.FromImageData(imageData);
                var processed = mat.Clone();

                // 调整大小
                if (options.Resize && options.TargetSize.HasValue)
                {
                    var targetSize = new OpenCvSharp.Size(options.TargetSize.Value.Width, options.TargetSize.Value.Height);
                    Cv2.Resize(processed, processed, targetSize);
                    _logger.LogDebug("图像已调整大小到: {Width}x{Height}", targetSize.Width, targetSize.Height);
                }

                // 转换为灰度
                if (options.GrayScale)
                {
                    Cv2.CvtColor(processed, processed, ColorConversionCodes.BGR2GRAY);
                    _logger.LogDebug("图像已转换为灰度");
                }

                // 高斯模糊
                if (options.GaussianBlur)
                {
                    var kernelSize = new OpenCvSharp.Size(options.BlurKernelSize, options.BlurKernelSize);
                    Cv2.GaussianBlur(processed, processed, kernelSize, 0);
                    _logger.LogDebug("应用高斯模糊，核大小: {KernelSize}", options.BlurKernelSize);
                }

                // 边缘检测
                if (options.EdgeDetection)
                {
                    if (processed.Channels() > 1)
                    {
                        Cv2.CvtColor(processed, processed, ColorConversionCodes.BGR2GRAY);
                    }
                    Cv2.Canny(processed, processed, options.EdgeThreshold1, options.EdgeThreshold2);
                    _logger.LogDebug("应用边缘检测，阈值: {Threshold1}, {Threshold2}", 
                        options.EdgeThreshold1, options.EdgeThreshold2);
                }

                var result = processed.ToBytes();
                _logger.LogDebug("图像预处理完成，输出大小: {Size} bytes", result.Length);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "图像预处理失败");
                throw new ComputerVisionException("图像预处理失败", ex);
            }
        });
    }

    public async Task<IEnumerable<ContourInfo>> DetectContoursAsync(byte[] imageData)
    {
        return await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("开始轮廓检测");

                using var mat = Mat.FromImageData(imageData);
                using var gray = new Mat();
                
                // 转换为灰度图
                if (mat.Channels() > 1)
                {
                    Cv2.CvtColor(mat, gray, ColorConversionCodes.BGR2GRAY);
                }
                else
                {
                    mat.CopyTo(gray);
                }

                // 查找轮廓
                Cv2.FindContours(gray, out var contours, out var hierarchy, 
                    RetrievalModes.External, ContourApproximationModes.ApproxSimple);

                var contourInfos = new List<ContourInfo>();

                for (int i = 0; i < contours.Length; i++)
                {
                    var contour = contours[i];
                    var area = Cv2.ContourArea(contour);
                    var perimeter = Cv2.ArcLength(contour, true);
                    var boundingRect = Cv2.BoundingRect(contour);

                    // 过滤太小的轮廓
                    if (area < 100) continue;

                    var points = contour.Select(p => new System.Drawing.Point(p.X, p.Y)).ToArray();

                    contourInfos.Add(new ContourInfo
                    {
                        Points = points,
                        BoundingRect = new Rectangle(boundingRect.X, boundingRect.Y, 
                            boundingRect.Width, boundingRect.Height),
                        Area = area,
                        Perimeter = perimeter
                    });
                }

                _logger.LogDebug("轮廓检测完成，找到 {Count} 个轮廓", contourInfos.Count);
                return contourInfos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "轮廓检测失败");
                throw new ComputerVisionException("轮廓检测失败", ex);
            }
        });
    }

    public async Task<TemplateMatchResult> MatchTemplateAsync(byte[] image, byte[] template)
    {
        return await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("开始模板匹配");

                using var imageMat = Mat.FromImageData(image);
                using var templateMat = Mat.FromImageData(template);
                using var result = new Mat();

                // 执行模板匹配
                Cv2.MatchTemplate(imageMat, templateMat, result, TemplateMatchModes.CCoeffNormed);

                // 查找最佳匹配位置
                Cv2.MinMaxLoc(result, out var minVal, out var maxVal, out var minLoc, out var maxLoc);

                var confidence = maxVal;
                var isMatch = confidence > 0.8; // 阈值可配置

                var matchRect = new Rectangle(maxLoc.X, maxLoc.Y, templateMat.Width, templateMat.Height);

                _logger.LogDebug("模板匹配完成，置信度: {Confidence}, 匹配: {IsMatch}, 位置: {Location}", 
                    confidence, isMatch, maxLoc);

                return new TemplateMatchResult
                {
                    Location = new System.Drawing.Point(maxLoc.X, maxLoc.Y),
                    Confidence = confidence,
                    MatchRect = matchRect,
                    IsMatch = isMatch
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模板匹配失败");
                throw new ComputerVisionException("模板匹配失败", ex);
            }
        });
    }

    public async Task<SegmentationResult> SegmentObjectAsync(byte[] imageData, System.Drawing.Point clickPoint)
    {
        return await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("开始对象分割，点击位置: ({X}, {Y})", clickPoint.X, clickPoint.Y);

                // 这里是一个简化的分割实现，使用 watershed 算法
                using var mat = Mat.FromImageData(imageData);
                using var gray = new Mat();
                using var binary = new Mat();
                using var markers = new Mat();

                // 转换为灰度
                Cv2.CvtColor(mat, gray, ColorConversionCodes.BGR2GRAY);

                // 二值化
                Cv2.Threshold(gray, binary, 0, 255, ThresholdTypes.Binary | ThresholdTypes.Otsu);

                // 创建标记
                markers.Create(mat.Size(), MatType.CV_32S);
                markers.SetTo(0);

                // 在点击位置设置前景标记
                Cv2.Circle(markers, new OpenCvSharp.Point(clickPoint.X, clickPoint.Y), 5, Scalar.White, -1);

                // 执行 watershed
                Cv2.Watershed(mat, markers);

                // 创建掩码
                using var mask = new Mat();
                markers.ConvertTo(mask, MatType.CV_8U);

                var maskBytes = mask.ToBytes();
                var boundingBox = Cv2.BoundingRect(mask);

                _logger.LogDebug("对象分割完成，边界框: {BoundingBox}", boundingBox);

                return new SegmentationResult
                {
                    Mask = maskBytes,
                    BoundingBox = new Rectangle(boundingBox.X, boundingBox.Y, 
                        boundingBox.Width, boundingBox.Height),
                    Confidence = 0.8f // 简化的置信度
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "对象分割失败");
                throw new ComputerVisionException("对象分割失败", ex);
            }
        });
    }

    public async Task<IEnumerable<ObjectSegment>> AutoSegmentAsync(byte[] imageData)
    {
        return await Task.Run(() =>
        {
            try
            {
                _logger.LogDebug("开始自动分割");

                // 这里是一个简化的自动分割实现
                var contours = DetectContoursAsync(imageData).Result;
                var segments = new List<ObjectSegment>();

                foreach (var contour in contours.Take(10)) // 限制数量
                {
                    // 为每个轮廓创建一个分割对象
                    segments.Add(new ObjectSegment
                    {
                        Mask = new byte[0], // 简化实现
                        BoundingBox = contour.BoundingRect,
                        Confidence = 0.7f,
                        Label = "Object"
                    });
                }

                _logger.LogDebug("自动分割完成，找到 {Count} 个对象", segments.Count);
                return segments;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "自动分割失败");
                throw new ComputerVisionException("自动分割失败", ex);
            }
        });
    }
}
