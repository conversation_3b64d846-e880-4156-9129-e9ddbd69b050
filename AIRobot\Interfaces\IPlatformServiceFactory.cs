namespace AIRobot.Core.Interfaces;

/// <summary>
/// 平台服务工厂接口
/// </summary>
public interface IPlatformServiceFactory
{
    /// <summary>
    /// 创建屏幕捕获服务
    /// </summary>
    IScreenCapture CreateScreenCapture();
    
    /// <summary>
    /// 创建输入模拟服务
    /// </summary>
    IInputSimulator CreateInputSimulator();
    
    /// <summary>
    /// 创建应用程序控制服务
    /// </summary>
    IApplicationController CreateApplicationController();
    
    /// <summary>
    /// 创建计算机视觉服务
    /// </summary>
    IComputerVisionService CreateComputerVisionService();
    
    /// <summary>
    /// 创建 OCR 服务
    /// </summary>
    IOcrService CreateOcrService();
    
    /// <summary>
    /// 获取当前平台名称
    /// </summary>
    string GetPlatformName();
    
    /// <summary>
    /// 检查平台是否支持
    /// </summary>
    bool IsPlatformSupported();
}
