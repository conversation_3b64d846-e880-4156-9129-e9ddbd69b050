#!/bin/bash
# AIRobot Python 环境设置脚本
# 用于配置 PaddleOCR 和相关依赖

set -e

PYTHON_CMD=${1:-python3}
FORCE=${2:-false}

echo "=== AIRobot Python 环境设置 ==="

# 检查 Python 是否安装
echo "检查 Python 安装..."
if ! command -v $PYTHON_CMD &> /dev/null; then
    echo "❌ 未找到 Python。请确保 Python 3.8+ 已安装。"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
    echo "macOS: brew install python3"
    exit 1
fi

PYTHON_VERSION=$($PYTHON_CMD --version 2>&1)
echo "✅ 找到 Python: $PYTHON_VERSION"

# 检查 Python 版本
if [[ $PYTHON_VERSION =~ Python\ ([0-9]+)\.([0-9]+) ]]; then
    MAJOR=${BASH_REMATCH[1]}
    MINOR=${BASH_REMATCH[2]}
    
    if [ $MAJOR -lt 3 ] || ([ $MAJOR -eq 3 ] && [ $MINOR -lt 8 ]); then
        echo "❌ Python 版本过低。需要 Python 3.8 或更高版本。"
        exit 1
    fi
else
    echo "⚠️ 无法解析 Python 版本，继续执行..."
fi

# 创建虚拟环境
VENV_PATH="python_env"
if [ -d "$VENV_PATH" ]; then
    if [ "$FORCE" = "true" ]; then
        echo "删除现有虚拟环境..."
        rm -rf "$VENV_PATH"
    else
        echo "虚拟环境已存在。"
        read -p "是否继续使用现有环境? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 0
        fi
    fi
fi

if [ ! -d "$VENV_PATH" ]; then
    echo "创建 Python 虚拟环境..."
    $PYTHON_CMD -m venv "$VENV_PATH"
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source "$VENV_PATH/bin/activate"

# 升级 pip
echo "升级 pip..."
python -m pip install --upgrade pip

# 安装依赖包
echo "安装 PaddleOCR 依赖..."
pip install -r python_requirements.txt

# 验证安装
echo "验证 PaddleOCR 安装..."
cat > test_paddleocr.py << 'EOF'
import paddleocr
import cv2
import numpy as np

print("PaddleOCR 版本:", paddleocr.__version__)
print("OpenCV 版本:", cv2.__version__)
print("NumPy 版本:", np.__version__)

# 测试 PaddleOCR 初始化
try:
    ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch')
    print("PaddleOCR 初始化成功")
except Exception as e:
    print("PaddleOCR 初始化失败:", str(e))
EOF

if python test_paddleocr.py; then
    echo "✅ PaddleOCR 环境配置成功!"
else
    echo "❌ PaddleOCR 环境配置失败"
    exit 1
fi

# 清理测试文件
rm -f test_paddleocr.py

echo ""
echo "=== 环境配置完成 ==="
echo "虚拟环境路径: $(pwd)/$VENV_PATH"
echo "激活命令: source $VENV_PATH/bin/activate"
echo ""
echo "下一步: 运行 AIRobot 应用程序测试 OCR 功能"
