# AIRobot 开发指南

## 1. 开发环境设置

### 1.1 必需软件

- **Visual Studio 2024** 或 **Visual Studio Code**
- **.NET 9 SDK** (最新版本)
- **MAUI 工作负载**

### 1.2 安装 MAUI 工作负载

```bash
dotnet workload install maui
```

### 1.3 验证安装

```bash
dotnet --version
dotnet workload list
```

## 2. 项目结构创建

### 2.1 解决方案结构

```
AIRobot.sln
├── src/
│   ├── AIRobot.Core/                 # 核心业务逻辑 (Class Library)
│   │   ├── Interfaces/               # 接口定义
│   │   ├── Models/                   # 数据模型
│   │   ├── Services/                 # 业务服务
│   │   └── Exceptions/               # 自定义异常
│   │
│   ├── AIRobot.Platforms/            # 平台特定实现 (Class Library)
│   │   ├── Windows/                  # Windows 实现
│   │   ├── MacOS/                    # macOS 实现
│   │   └── Android/                  # Android 实现
│   │
│   ├── AIRobot.MAUI/                # MAUI 主项目
│   │   ├── Components/               # Blazor 组件
│   │   ├── Pages/                    # 页面
│   │   ├── Services/                 # UI 服务
│   │   └── Platforms/                # 平台特定代码
│   │
│   └── AIRobot.Shared/              # 共享组件 (Razor Class Library)
│       ├── Components/               # 可重用组件
│       └── wwwroot/                  # 静态资源
│
├── tests/
│   ├── AIRobot.Core.Tests/          # 核心逻辑测试
│   ├── AIRobot.Platforms.Tests/     # 平台实现测试
│   └── AIRobot.MAUI.Tests/          # UI 测试
│
└── docs/                            # 文档
```

### 2.2 创建项目命令

```bash
# 创建解决方案
dotnet new sln -n AIRobot

# 创建核心类库
dotnet new classlib -n AIRobot.Core -o src/AIRobot.Core
dotnet sln add src/AIRobot.Core/AIRobot.Core.csproj

# 创建平台实现类库
dotnet new classlib -n AIRobot.Platforms -o src/AIRobot.Platforms
dotnet sln add src/AIRobot.Platforms/AIRobot.Platforms.csproj

# 创建 MAUI 项目
dotnet new maui-blazor -n AIRobot.MAUI -o src/AIRobot.MAUI
dotnet sln add src/AIRobot.MAUI/AIRobot.MAUI.csproj

# 创建共享组件库
dotnet new razorclasslib -n AIRobot.Shared -o src/AIRobot.Shared
dotnet sln add src/AIRobot.Shared/AIRobot.Shared.csproj

# 创建测试项目
dotnet new xunit -n AIRobot.Core.Tests -o tests/AIRobot.Core.Tests
dotnet sln add tests/AIRobot.Core.Tests/AIRobot.Core.Tests.csproj

dotnet new xunit -n AIRobot.Platforms.Tests -o tests/AIRobot.Platforms.Tests
dotnet sln add tests/AIRobot.Platforms.Tests/AIRobot.Platforms.Tests.csproj
```

## 3. 依赖包配置

### 3.1 AIRobot.Core 依赖

```xml
<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
<PackageReference Include="System.Drawing.Common" Version="9.0.0" />
```

### 3.2 AIRobot.Platforms 依赖

```xml
<PackageReference Include="Tesseract" Version="5.2.0" />
<PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
<PackageReference Include="System.Drawing.Common" Version="9.0.0" />
```

### 3.3 AIRobot.MAUI 依赖

```xml
<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.0" />
<PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="9.0.0" />
<PackageReference Include="CommunityToolkit.Maui" Version="9.0.0" />
```

## 4. 编码规范

### 4.1 命名约定

- **类名**: PascalCase (例: `ScreenCapture`)
- **接口名**: 以 I 开头的 PascalCase (例: `IScreenCapture`)
- **方法名**: PascalCase (例: `CaptureScreenAsync`)
- **属性名**: PascalCase (例: `WindowTitle`)
- **字段名**: camelCase，私有字段以下划线开头 (例: `_logger`)
- **常量名**: UPPER_CASE (例: `MAX_RETRY_COUNT`)

### 4.2 异步编程规范

- 所有异步方法以 `Async` 结尾
- 使用 `Task` 或 `Task<T>` 返回类型
- 避免 `async void`，除非是事件处理程序
- 使用 `ConfigureAwait(false)` 在库代码中

```csharp
public async Task<byte[]> CaptureScreenAsync()
{
    // 实现代码
    return await SomeAsyncOperation().ConfigureAwait(false);
}
```

### 4.3 异常处理

- 使用自定义异常类型
- 提供有意义的错误消息
- 记录异常详情

```csharp
public class AutomationException : Exception
{
    public AutomationException(string message) : base(message) { }
    public AutomationException(string message, Exception innerException) 
        : base(message, innerException) { }
}
```

### 4.4 日志记录

```csharp
public class ScreenCaptureService
{
    private readonly ILogger<ScreenCaptureService> _logger;

    public ScreenCaptureService(ILogger<ScreenCaptureService> logger)
    {
        _logger = logger;
    }

    public async Task<byte[]> CaptureAsync()
    {
        _logger.LogInformation("开始屏幕捕获");
        try
        {
            // 实现代码
            _logger.LogInformation("屏幕捕获成功");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "屏幕捕获失败");
            throw;
        }
    }
}
```

## 5. 测试策略

### 5.1 单元测试

```csharp
[Fact]
public async Task CaptureScreenAsync_ShouldReturnValidImageData()
{
    // Arrange
    var mockFactory = new Mock<IPlatformServiceFactory>();
    var mockCapture = new Mock<IScreenCapture>();
    mockCapture.Setup(x => x.CaptureScreenAsync())
              .ReturnsAsync(new byte[] { 1, 2, 3, 4 });
    mockFactory.Setup(x => x.CreateScreenCapture())
               .Returns(mockCapture.Object);

    var service = new ScreenCaptureService(mockFactory.Object);

    // Act
    var result = await service.CaptureScreenAsync();

    // Assert
    Assert.NotNull(result);
    Assert.True(result.Length > 0);
}
```

### 5.2 集成测试

```csharp
[Fact]
public async Task EndToEndAutomation_ShouldCompleteSuccessfully()
{
    // 测试完整的自动化流程
    var automationService = new AutomationService(/* dependencies */);
    
    var task = new AutomationTask
    {
        Steps = new[]
        {
            new ClickStep { Position = new Point(100, 100) },
            new TypeTextStep { Text = "Hello World" }
        }
    };

    var result = await automationService.ExecuteAsync(task);
    
    Assert.True(result.IsSuccess);
}
```

## 6. 调试技巧

### 6.1 平台特定调试

```csharp
#if WINDOWS
    // Windows 特定调试代码
    Debug.WriteLine($"Windows Handle: {windowHandle}");
#elif MACCATALYST
    // macOS 特定调试代码
    Debug.WriteLine($"macOS Window ID: {windowId}");
#endif
```

### 6.2 性能监控

```csharp
public async Task<byte[]> CaptureScreenAsync()
{
    using var activity = ActivitySource.StartActivity("ScreenCapture");
    var stopwatch = Stopwatch.StartNew();
    
    try
    {
        var result = await InternalCaptureAsync();
        activity?.SetTag("success", true);
        return result;
    }
    catch (Exception ex)
    {
        activity?.SetTag("success", false);
        activity?.SetTag("error", ex.Message);
        throw;
    }
    finally
    {
        stopwatch.Stop();
        _logger.LogInformation("屏幕捕获耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
    }
}
```

## 7. 部署和发布

### 7.1 Windows 发布

```bash
dotnet publish -c Release -f net9.0-windows10.0.19041.0 --self-contained
```

### 7.2 macOS 发布

```bash
dotnet publish -c Release -f net9.0-maccatalyst --self-contained
```

### 7.3 Android 发布

```bash
dotnet publish -c Release -f net9.0-android --self-contained
```

## 8. 持续集成

### 8.1 GitHub Actions 配置

```yaml
name: CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v4
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '9.0.x'
    - name: Restore dependencies
      run: dotnet restore
    - name: Build
      run: dotnet build --no-restore
    - name: Test
      run: dotnet test --no-build --verbosity normal
```

## 9. 版本控制

### 9.1 Git 工作流

- **main**: 生产就绪代码
- **develop**: 开发分支
- **feature/***: 功能分支
- **hotfix/***: 热修复分支

### 9.2 提交消息规范

```
feat: 添加屏幕捕获功能
fix: 修复 Windows 输入模拟问题
docs: 更新 API 文档
test: 添加单元测试
refactor: 重构平台工厂类
```

## 10. 下一步行动计划

### 10.1 第一阶段 - 项目基础搭建 (1-2 天)
1. 创建解决方案和项目结构
2. 配置依赖包和项目引用
3. 定义核心接口和数据模型
4. 设置基础的依赖注入容器

### 10.2 第二阶段 - Windows 平台核心功能 (3-5 天)
1. 实现 Windows 屏幕捕获功能
2. 实现 Windows 输入模拟功能
3. 实现 Windows 应用程序控制功能
4. 集成 OCR 功能 (Tesseract)

### 10.3 第三阶段 - UI 界面开发 (2-3 天)
1. 设计 Blazor 用户界面
2. 实现自动化任务配置界面
3. 实现实时预览和调试功能
4. 添加日志和错误显示

### 10.4 第四阶段 - 测试和优化 (2-3 天)
1. 编写单元测试和集成测试
2. 性能优化和内存管理
3. 错误处理和用户体验改进
4. 文档完善和示例代码

### 10.5 第五阶段 - 多平台扩展 (后续)
1. macOS 平台适配
2. Android 平台适配
3. 跨平台兼容性测试
4. 发布和部署流程
