using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace AIRobot.Core.Services;

/// <summary>
/// 性能监控服务
/// 提供性能指标收集、分析和报告功能
/// </summary>
public class PerformanceMonitor : IDisposable
{
    private readonly ILogger<PerformanceMonitor> _logger;
    private readonly ConcurrentDictionary<string, PerformanceMetric> _metrics;
    private readonly Timer _reportTimer;
    private readonly Process _currentProcess;
    private bool _disposed = false;

    public PerformanceMonitor(ILogger<PerformanceMonitor> logger)
    {
        _logger = logger;
        _metrics = new ConcurrentDictionary<string, PerformanceMetric>();
        _currentProcess = Process.GetCurrentProcess();
        
        // 每分钟生成一次性能报告
        _reportTimer = new Timer(GeneratePerformanceReport, null, 
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        
        _logger.LogInformation("性能监控服务已启动");
    }

    /// <summary>
    /// 测量操作执行时间
    /// </summary>
    public async Task<T> MeasureAsync<T>(string operationName, Func<Task<T>> operation)
    {
        var stopwatch = Stopwatch.StartNew();
        var startMemory = GC.GetTotalMemory(false);
        
        try
        {
            var result = await operation();
            stopwatch.Stop();
            
            var endMemory = GC.GetTotalMemory(false);
            var memoryDelta = endMemory - startMemory;
            
            RecordMetric(operationName, stopwatch.Elapsed, memoryDelta, true);
            
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordMetric(operationName, stopwatch.Elapsed, 0, false);
            
            _logger.LogError(ex, "操作 {Operation} 执行失败", operationName);
            throw;
        }
    }

    /// <summary>
    /// 测量同步操作执行时间
    /// </summary>
    public T Measure<T>(string operationName, Func<T> operation)
    {
        var stopwatch = Stopwatch.StartNew();
        var startMemory = GC.GetTotalMemory(false);
        
        try
        {
            var result = operation();
            stopwatch.Stop();
            
            var endMemory = GC.GetTotalMemory(false);
            var memoryDelta = endMemory - startMemory;
            
            RecordMetric(operationName, stopwatch.Elapsed, memoryDelta, true);
            
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordMetric(operationName, stopwatch.Elapsed, 0, false);
            
            _logger.LogError(ex, "操作 {Operation} 执行失败", operationName);
            throw;
        }
    }

    /// <summary>
    /// 记录自定义指标
    /// </summary>
    public void RecordMetric(string name, TimeSpan duration, long memoryDelta = 0, bool success = true)
    {
        _metrics.AddOrUpdate(name, 
            new PerformanceMetric(name),
            (key, existing) =>
            {
                existing.AddMeasurement(duration, memoryDelta, success);
                return existing;
            });
    }

    /// <summary>
    /// 获取指定操作的性能指标
    /// </summary>
    public PerformanceMetric? GetMetric(string operationName)
    {
        return _metrics.TryGetValue(operationName, out var metric) ? metric : null;
    }

    /// <summary>
    /// 获取所有性能指标
    /// </summary>
    public IReadOnlyDictionary<string, PerformanceMetric> GetAllMetrics()
    {
        return _metrics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    /// <summary>
    /// 获取系统性能信息
    /// </summary>
    public SystemPerformanceInfo GetSystemPerformanceInfo()
    {
        try
        {
            _currentProcess.Refresh();
            
            return new SystemPerformanceInfo
            {
                ProcessId = _currentProcess.Id,
                WorkingSetMemory = _currentProcess.WorkingSet64,
                PrivateMemory = _currentProcess.PrivateMemorySize64,
                VirtualMemory = _currentProcess.VirtualMemorySize64,
                GCTotalMemory = GC.GetTotalMemory(false),
                ProcessorTime = _currentProcess.TotalProcessorTime,
                ThreadCount = _currentProcess.Threads.Count,
                HandleCount = _currentProcess.HandleCount,
                StartTime = _currentProcess.StartTime,
                ProcessorCount = Environment.ProcessorCount,
                MachineName = Environment.MachineName,
                OSVersion = Environment.OSVersion.ToString(),
                CLRVersion = Environment.Version.ToString()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统性能信息失败");
            return new SystemPerformanceInfo();
        }
    }

    /// <summary>
    /// 运行性能基准测试
    /// </summary>
    public async Task<BenchmarkResult> RunBenchmarkAsync(string name, Func<Task> operation, int iterations = 10)
    {
        _logger.LogInformation("开始性能基准测试: {Name}, 迭代次数: {Iterations}", name, iterations);
        
        var results = new List<TimeSpan>();
        var memoryResults = new List<long>();
        var errors = 0;

        // 预热
        try
        {
            await operation();
        }
        catch
        {
            // 忽略预热错误
        }

        // 强制垃圾回收
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var baselineMemory = GC.GetTotalMemory(false);

        for (int i = 0; i < iterations; i++)
        {
            var stopwatch = Stopwatch.StartNew();
            var startMemory = GC.GetTotalMemory(false);
            
            try
            {
                await operation();
                stopwatch.Stop();
                
                var endMemory = GC.GetTotalMemory(false);
                results.Add(stopwatch.Elapsed);
                memoryResults.Add(endMemory - startMemory);
            }
            catch (Exception ex)
            {
                errors++;
                _logger.LogWarning(ex, "基准测试迭代 {Iteration} 失败", i + 1);
            }
        }

        var benchmark = new BenchmarkResult
        {
            Name = name,
            Iterations = iterations,
            SuccessfulIterations = iterations - errors,
            ErrorCount = errors,
            MinTime = results.Any() ? results.Min() : TimeSpan.Zero,
            MaxTime = results.Any() ? results.Max() : TimeSpan.Zero,
            AverageTime = results.Any() ? TimeSpan.FromTicks((long)results.Average(t => t.Ticks)) : TimeSpan.Zero,
            MedianTime = results.Any() ? results.OrderBy(t => t).ElementAt(results.Count / 2) : TimeSpan.Zero,
            StandardDeviation = CalculateStandardDeviation(results),
            TotalMemoryAllocated = memoryResults.Sum(),
            AverageMemoryPerIteration = memoryResults.Any() ? memoryResults.Average() : 0,
            Timestamp = DateTime.UtcNow
        };

        _logger.LogInformation("基准测试完成: {Name}, 平均时间: {AvgTime}ms, 成功率: {SuccessRate:P}", 
            name, benchmark.AverageTime.TotalMilliseconds, 
            (double)benchmark.SuccessfulIterations / iterations);

        return benchmark;
    }

    /// <summary>
    /// 清除所有指标
    /// </summary>
    public void ClearMetrics()
    {
        _metrics.Clear();
        _logger.LogInformation("已清除所有性能指标");
    }

    /// <summary>
    /// 导出性能报告
    /// </summary>
    public async Task<string> ExportPerformanceReportAsync(string format = "json")
    {
        var report = new PerformanceReport
        {
            GeneratedAt = DateTime.UtcNow,
            SystemInfo = GetSystemPerformanceInfo(),
            Metrics = GetAllMetrics().Values.ToList()
        };

        switch (format.ToLower())
        {
            case "json":
                return System.Text.Json.JsonSerializer.Serialize(report, new System.Text.Json.JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
            
            case "csv":
                return GenerateCsvReport(report);
            
            default:
                throw new ArgumentException($"不支持的报告格式: {format}");
        }
    }

    private void GeneratePerformanceReport(object? state)
    {
        try
        {
            var systemInfo = GetSystemPerformanceInfo();
            var topMetrics = _metrics.Values
                .OrderByDescending(m => m.TotalCalls)
                .Take(10)
                .ToList();

            _logger.LogInformation("性能报告 - 内存使用: {Memory}MB, 线程数: {Threads}, 活跃指标: {Metrics}",
                systemInfo.WorkingSetMemory / 1024 / 1024,
                systemInfo.ThreadCount,
                _metrics.Count);

            foreach (var metric in topMetrics)
            {
                _logger.LogDebug("指标 {Name}: 调用 {Calls} 次, 平均 {Avg}ms, 成功率 {Success:P}",
                    metric.Name, metric.TotalCalls, metric.AverageTime.TotalMilliseconds, metric.SuccessRate);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成性能报告失败");
        }
    }

    private double CalculateStandardDeviation(List<TimeSpan> values)
    {
        if (!values.Any()) return 0;

        var average = values.Average(t => t.TotalMilliseconds);
        var sumOfSquares = values.Sum(t => Math.Pow(t.TotalMilliseconds - average, 2));
        return Math.Sqrt(sumOfSquares / values.Count);
    }

    private string GenerateCsvReport(PerformanceReport report)
    {
        var csv = new StringBuilder();
        csv.AppendLine("Metric Name,Total Calls,Success Rate,Average Time (ms),Min Time (ms),Max Time (ms),Total Memory (bytes)");
        
        foreach (var metric in report.Metrics)
        {
            csv.AppendLine($"{metric.Name},{metric.TotalCalls},{metric.SuccessRate:F4}," +
                          $"{metric.AverageTime.TotalMilliseconds:F2},{metric.MinTime.TotalMilliseconds:F2}," +
                          $"{metric.MaxTime.TotalMilliseconds:F2},{metric.TotalMemoryAllocated}");
        }
        
        return csv.ToString();
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _reportTimer?.Dispose();
            _currentProcess?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// 性能指标
/// </summary>
public class PerformanceMetric
{
    private readonly object _lock = new();
    private readonly List<TimeSpan> _durations = new();
    private readonly List<long> _memoryDeltas = new();
    
    public string Name { get; }
    public int TotalCalls { get; private set; }
    public int SuccessfulCalls { get; private set; }
    public int FailedCalls { get; private set; }
    public TimeSpan TotalTime { get; private set; }
    public long TotalMemoryAllocated { get; private set; }
    public DateTime FirstCall { get; private set; }
    public DateTime LastCall { get; private set; }

    public PerformanceMetric(string name)
    {
        Name = name;
        FirstCall = DateTime.UtcNow;
    }

    public void AddMeasurement(TimeSpan duration, long memoryDelta, bool success)
    {
        lock (_lock)
        {
            TotalCalls++;
            if (success) SuccessfulCalls++; else FailedCalls++;
            
            TotalTime = TotalTime.Add(duration);
            TotalMemoryAllocated += Math.Max(0, memoryDelta);
            LastCall = DateTime.UtcNow;
            
            _durations.Add(duration);
            _memoryDeltas.Add(memoryDelta);
        }
    }

    public TimeSpan AverageTime => TotalCalls > 0 ? TimeSpan.FromTicks(TotalTime.Ticks / TotalCalls) : TimeSpan.Zero;
    public TimeSpan MinTime => _durations.Any() ? _durations.Min() : TimeSpan.Zero;
    public TimeSpan MaxTime => _durations.Any() ? _durations.Max() : TimeSpan.Zero;
    public double SuccessRate => TotalCalls > 0 ? (double)SuccessfulCalls / TotalCalls : 0;
    public double AverageMemoryPerCall => TotalCalls > 0 ? (double)TotalMemoryAllocated / TotalCalls : 0;
}

/// <summary>
/// 系统性能信息
/// </summary>
public class SystemPerformanceInfo
{
    public int ProcessId { get; set; }
    public long WorkingSetMemory { get; set; }
    public long PrivateMemory { get; set; }
    public long VirtualMemory { get; set; }
    public long GCTotalMemory { get; set; }
    public TimeSpan ProcessorTime { get; set; }
    public int ThreadCount { get; set; }
    public int HandleCount { get; set; }
    public DateTime StartTime { get; set; }
    public int ProcessorCount { get; set; }
    public string MachineName { get; set; } = "";
    public string OSVersion { get; set; } = "";
    public string CLRVersion { get; set; } = "";
}

/// <summary>
/// 基准测试结果
/// </summary>
public class BenchmarkResult
{
    public string Name { get; set; } = "";
    public int Iterations { get; set; }
    public int SuccessfulIterations { get; set; }
    public int ErrorCount { get; set; }
    public TimeSpan MinTime { get; set; }
    public TimeSpan MaxTime { get; set; }
    public TimeSpan AverageTime { get; set; }
    public TimeSpan MedianTime { get; set; }
    public double StandardDeviation { get; set; }
    public long TotalMemoryAllocated { get; set; }
    public double AverageMemoryPerIteration { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 性能报告
/// </summary>
public class PerformanceReport
{
    public DateTime GeneratedAt { get; set; }
    public SystemPerformanceInfo SystemInfo { get; set; } = new();
    public List<PerformanceMetric> Metrics { get; set; } = new();
}
