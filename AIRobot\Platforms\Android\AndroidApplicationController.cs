using System.Diagnostics;
using AIRobot.Core.Interfaces;
using AIRobot.Core.Models;
using Microsoft.Extensions.Logging;

#if ANDROID
using Android.App;
using Android.Content;
using Android.Content.PM;
using AndroidX.Core.Content;
using Java.Lang;
#endif

namespace AIRobot.Platforms.Android;

/// <summary>
/// Android平台应用程序控制器实现
/// </summary>
public class AndroidApplicationController : IApplicationController
{
    private readonly ILogger<AndroidApplicationController> _logger;

#if ANDROID
    private readonly Context _context;

    public AndroidApplicationController(ILogger<AndroidApplicationController> logger, Context context)
    {
        _logger = logger;
        _context = context;
    }
#else
    public AndroidApplicationController(ILogger<AndroidApplicationController> logger)
    {
        _logger = logger;
    }
#endif

    public async Task<Process> StartApplicationAsync(string applicationPath, string? arguments = null)
    {
        try
        {
            _logger.LogInformation($"启动Android应用程序: {applicationPath}");

#if ANDROID
            var packageManager = _context.PackageManager;
            if (packageManager == null)
            {
                throw new InvalidOperationException("无法获取PackageManager");
            }

            Intent? launchIntent;
            
            // 如果是包名，直接启动
            if (IsPackageName(applicationPath))
            {
                launchIntent = packageManager.GetLaunchIntentForPackage(applicationPath);
            }
            else
            {
                // 如果是应用名称，先查找包名
                var packageName = await FindPackageByNameAsync(applicationPath);
                if (packageName != null)
                {
                    launchIntent = packageManager.GetLaunchIntentForPackage(packageName);
                }
                else
                {
                    throw new ArgumentException($"找不到应用程序: {applicationPath}");
                }
            }

            if (launchIntent == null)
            {
                throw new InvalidOperationException($"无法创建启动Intent: {applicationPath}");
            }

            // 添加参数
            if (!string.IsNullOrEmpty(arguments))
            {
                // Android中通过Extra传递参数
                var args = arguments.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < args.Length; i++)
                {
                    launchIntent.PutExtra($"arg_{i}", args[i]);
                }
            }

            launchIntent.AddFlags(ActivityFlags.NewTask);
            _context.StartActivity(launchIntent);

            // 创建一个虚拟的Process对象
            var process = new Process();
            
            _logger.LogInformation($"Android应用程序启动成功: {applicationPath}");
            return process;
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, $"启动Android应用程序失败: {applicationPath}");
            throw;
        }
    }

    public async Task<bool> IsApplicationRunningAsync(string applicationName)
    {
        try
        {
            _logger.LogInformation($"检查Android应用程序运行状态: {applicationName}");

#if ANDROID
            var activityManager = _context.GetSystemService(Context.ActivityService) as ActivityManager;
            if (activityManager == null)
            {
                return false;
            }

            var runningApps = activityManager.GetRunningAppProcesses();
            if (runningApps == null)
            {
                return false;
            }

            var packageName = IsPackageName(applicationName) ? applicationName : await FindPackageByNameAsync(applicationName);
            if (packageName == null)
            {
                return false;
            }

            var isRunning = runningApps.Any(app => app.ProcessName.Equals(packageName, StringComparison.OrdinalIgnoreCase));
            
            _logger.LogInformation($"Android应用程序 {applicationName} 运行状态: {isRunning}");
            return isRunning;
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, $"检查Android应用程序运行状态失败: {applicationName}");
            return false;
        }
    }

    public async Task<WindowInfo?> FindWindowAsync(string windowTitle)
    {
        try
        {
            _logger.LogInformation($"查找Android窗口: {windowTitle}");

#if ANDROID
            // Android中没有传统的窗口概念，这里查找匹配的应用程序
            var activityManager = _context.GetSystemService(Context.ActivityService) as ActivityManager;
            if (activityManager == null)
            {
                return null;
            }

            var runningTasks = activityManager.GetRunningTasks(10);
            foreach (var task in runningTasks)
            {
                if (task.TopActivity != null)
                {
                    var packageManager = _context.PackageManager;
                    try
                    {
                        var appInfo = packageManager?.GetApplicationInfo(task.TopActivity.PackageName, 0);
                        var appName = packageManager?.GetApplicationLabel(appInfo)?.ToString() ?? task.TopActivity.PackageName;
                        
                        if (appName.Contains(windowTitle, StringComparison.OrdinalIgnoreCase))
                        {
                            var windowInfo = new WindowInfo
                            {
                                Handle = new IntPtr(task.Id),
                                Title = appName,
                                ProcessName = task.TopActivity.PackageName,
                                Bounds = GetScreenBounds(),
                                IsVisible = true,
                                IsMinimized = false
                            };
                            
                            _logger.LogInformation($"找到Android窗口: {windowTitle}");
                            return windowInfo;
                        }
                    }
                    catch (System.Exception ex)
                    {
                        _logger.LogWarning(ex, $"获取应用信息失败: {task.TopActivity.PackageName}");
                    }
                }
            }

            _logger.LogInformation($"未找到Android窗口: {windowTitle}");
            return null;
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, $"查找Android窗口失败: {windowTitle}");
            return null;
        }
    }

    public async Task<bool> ActivateWindowAsync(IntPtr windowHandle)
    {
        try
        {
            _logger.LogInformation($"激活Android窗口: {windowHandle}");

#if ANDROID
            // Android中通过任务ID来切换应用
            var activityManager = _context.GetSystemService(Context.ActivityService) as ActivityManager;
            if (activityManager == null)
            {
                return false;
            }

            var taskId = windowHandle.ToInt32();
            activityManager.MoveTaskToFront(taskId, MoveTaskFlags.WithHome);
            
            _logger.LogInformation($"Android窗口激活成功: {windowHandle}");
            return true;
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, $"激活Android窗口失败: {windowHandle}");
            return false;
        }
    }

    public async Task<bool> CloseWindowAsync(IntPtr windowHandle)
    {
        try
        {
            _logger.LogInformation($"关闭Android窗口: {windowHandle}");

#if ANDROID
            // Android中关闭应用通常通过杀死进程
            var activityManager = _context.GetSystemService(Context.ActivityService) as ActivityManager;
            if (activityManager == null)
            {
                return false;
            }

            var taskId = windowHandle.ToInt32();
            
            // 尝试优雅关闭
            var runningTasks = activityManager.GetRunningTasks(50);
            var targetTask = runningTasks.FirstOrDefault(t => t.Id == taskId);
            
            if (targetTask?.TopActivity != null)
            {
                // 发送关闭Intent
                var closeIntent = new Intent(Intent.ActionMain);
                closeIntent.AddCategory(Intent.CategoryHome);
                closeIntent.SetFlags(ActivityFlags.NewTask);
                _context.StartActivity(closeIntent);
                
                _logger.LogInformation($"Android窗口关闭成功: {windowHandle}");
                return true;
            }

            return false;
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, $"关闭Android窗口失败: {windowHandle}");
            return false;
        }
    }

    public async Task<bool> MinimizeWindowAsync(IntPtr windowHandle)
    {
        try
        {
            _logger.LogInformation($"最小化Android窗口: {windowHandle}");

#if ANDROID
            // Android中最小化相当于回到主屏幕
            var homeIntent = new Intent(Intent.ActionMain);
            homeIntent.AddCategory(Intent.CategoryHome);
            homeIntent.SetFlags(ActivityFlags.NewTask);
            _context.StartActivity(homeIntent);
            
            _logger.LogInformation($"Android窗口最小化成功: {windowHandle}");
            return true;
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, $"最小化Android窗口失败: {windowHandle}");
            return false;
        }
    }

    public async Task<bool> MaximizeWindowAsync(IntPtr windowHandle)
    {
        try
        {
            _logger.LogInformation($"最大化Android窗口: {windowHandle}");

#if ANDROID
            // Android应用默认就是全屏的，这里直接激活窗口
            return await ActivateWindowAsync(windowHandle);
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, $"最大化Android窗口失败: {windowHandle}");
            return false;
        }
    }

    public async Task<IEnumerable<WindowInfo>> GetVisibleWindowsAsync()
    {
        try
        {
            _logger.LogInformation("获取Android可见窗口列表");

#if ANDROID
            var windows = new List<WindowInfo>();
            
            var activityManager = _context.GetSystemService(Context.ActivityService) as ActivityManager;
            if (activityManager == null)
            {
                return windows;
            }

            var runningTasks = activityManager.GetRunningTasks(10);
            foreach (var task in runningTasks)
            {
                if (task.TopActivity != null)
                {
                    var packageManager = _context.PackageManager;
                    try
                    {
                        var appInfo = packageManager?.GetApplicationInfo(task.TopActivity.PackageName, 0);
                        var appName = packageManager?.GetApplicationLabel(appInfo)?.ToString() ?? task.TopActivity.PackageName;
                        
                        var windowInfo = new WindowInfo
                        {
                            Handle = new IntPtr(task.Id),
                            Title = appName,
                            ProcessName = task.TopActivity.PackageName,
                            Bounds = GetScreenBounds(),
                            IsVisible = true,
                            IsMinimized = false
                        };
                        
                        windows.Add(windowInfo);
                    }
                    catch (System.Exception ex)
                    {
                        _logger.LogWarning(ex, $"获取应用信息失败: {task.TopActivity.PackageName}");
                    }
                }
            }

            _logger.LogInformation($"获取到 {windows.Count} 个Android可见窗口");
            return windows;
#else
            throw new PlatformNotSupportedException("此功能仅在Android平台上可用");
#endif
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "获取Android可见窗口列表失败");
            return Enumerable.Empty<WindowInfo>();
        }
    }

#if ANDROID
    private bool IsPackageName(string name)
    {
        // 简单判断是否为包名格式
        return name.Contains('.') && !name.Contains(' ');
    }

    private async Task<string?> FindPackageByNameAsync(string appName)
    {
        try
        {
            var packageManager = _context.PackageManager;
            if (packageManager == null) return null;

            var installedApps = packageManager.GetInstalledApplications(PackageInfoFlags.MetaData);
            
            foreach (var app in installedApps)
            {
                var label = packageManager.GetApplicationLabel(app)?.ToString();
                if (label != null && label.Contains(appName, StringComparison.OrdinalIgnoreCase))
                {
                    return app.PackageName;
                }
            }

            return null;
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, $"查找包名失败: {appName}");
            return null;
        }
    }

    private System.Drawing.Rectangle GetScreenBounds()
    {
        var displayMetrics = _context.Resources?.DisplayMetrics;
        if (displayMetrics != null)
        {
            return new System.Drawing.Rectangle(0, 0, displayMetrics.WidthPixels, displayMetrics.HeightPixels);
        }
        return new System.Drawing.Rectangle(0, 0, 1080, 1920); // 默认分辨率
    }
#endif
}
