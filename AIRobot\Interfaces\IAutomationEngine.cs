using AIRobot.Core.Models;

namespace AIRobot.Core.Interfaces;

/// <summary>
/// 自动化任务引擎接口
/// </summary>
public interface IAutomationEngine
{
    /// <summary>
    /// 执行自动化任务
    /// </summary>
    Task<TaskExecutionResult> ExecuteTaskAsync(AutomationTask task, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 执行单个步骤
    /// </summary>
    Task<StepResult> ExecuteStepAsync(AutomationStep step, IAutomationContext context);
    
    /// <summary>
    /// 暂停任务执行
    /// </summary>
    Task PauseTaskAsync(string taskId);
    
    /// <summary>
    /// 恢复任务执行
    /// </summary>
    Task ResumeTaskAsync(string taskId);
    
    /// <summary>
    /// 取消任务执行
    /// </summary>
    Task CancelTaskAsync(string taskId);
    
    /// <summary>
    /// 获取任务执行状态
    /// </summary>
    TaskExecutionStatus GetTaskStatus(string taskId);
    
    /// <summary>
    /// 任务执行状态变化事件
    /// </summary>
    event EventHandler<TaskStatusChangedEventArgs> TaskStatusChanged;
}

/// <summary>
/// 任务状态变化事件参数
/// </summary>
public class TaskStatusChangedEventArgs : EventArgs
{
    public string TaskId { get; set; } = string.Empty;
    public TaskExecutionStatus OldStatus { get; set; }
    public TaskExecutionStatus NewStatus { get; set; }
    public string Message { get; set; } = string.Empty;
}
